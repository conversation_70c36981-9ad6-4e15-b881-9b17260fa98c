#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - Flask Web应用
Wanyang Battery Monitoring System - Flask Web App
"""

from flask import Flask, render_template, jsonify, request
import json
import threading
import time
import random
from datetime import datetime

app = Flask(__name__, 
           template_folder='web/templates',
           static_folder='web/static')

# 电池数据
battery_data = {
    "soc": 98,
    "total_voltage": 84.8,
    "total_current": 0.0,
    "total_power": 0.0,
    "mos_temp": 34,
    "env_temp": 34,
    "remaining_capacity": 52,
    "cycle_count": 1,
    "battery_code": "BT2072055010032504140408",
    "update_time": "2025/6/18 12:28:42",
    "cell_voltages": [
        4.242, 4.244, 4.244, 4.244, 4.243,
        4.244, 4.241, 4.244, 4.243, 4.245,
        4.239, 4.244, 4.244, 4.243, 4.243,
        4.244, 4.244, 4.243, 4.243, 4.242
    ],
    "charge_status": False,
    "discharge_status": False,
    "protection_status": {
        "静置": True,
        "允许充电打开": True,
        "充电MOS正常导通": True,
        "充电MOS正常导通": True
    }
}

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    """获取电池数据API"""
    return jsonify(battery_data)

@app.route('/api/command', methods=['POST'])
def send_command():
    """发送控制命令API"""
    try:
        data = request.get_json()
        command = data.get('command')
        
        print(f"🎮 执行控制命令: {command}")
        
        # 模拟命令执行结果
        command_results = {
            'refresh_data': '刷新数据成功',
            'auto_refresh': '开启自动刷新',
            'reset_terminal': '终端复位成功',
            'buzzer_on': '开启蜂鸣器成功',
            'buzzer_off': '关闭蜂鸣器成功',
            'allow_charge': '允许充电成功',
            'forbid_charge': '禁止充电成功',
            'allow_discharge': '允许放电成功',
            'forbid_discharge': '禁止放电成功'
        }
        
        return jsonify({
            'success': True,
            'message': command_results.get(command, f'{command} 执行成功')
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'命令执行失败: {str(e)}'
        }), 500

def update_battery_data():
    """后台更新电池数据"""
    while True:
        time.sleep(3)  # 每3秒更新一次
        
        # 模拟数据变化
        battery_data['soc'] = min(100, max(0, battery_data['soc'] + random.uniform(-0.1, 0.1)))
        battery_data['total_voltage'] += random.uniform(-0.02, 0.02)
        battery_data['total_current'] += random.uniform(-0.1, 0.1)
        battery_data['total_power'] = battery_data['total_voltage'] * battery_data['total_current']
        battery_data['mos_temp'] += random.randint(-1, 1)
        battery_data['env_temp'] += random.randint(-1, 1)
        battery_data['update_time'] = datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        
        # 更新电芯电压
        for i in range(len(battery_data['cell_voltages'])):
            battery_data['cell_voltages'][i] += random.uniform(-0.001, 0.001)
            battery_data['cell_voltages'][i] = round(battery_data['cell_voltages'][i], 3)

if __name__ == '__main__':
    print("🔋 万洋锂电池监控系统 - Web版本")
    print("=" * 50)
    print("🌐 启动Flask Web服务器...")
    print("📱 访问地址:")
    print("   • 本机: http://localhost:5000")
    print("   • 手机: http://你的IP地址:5000")
    print("🔄 数据每3秒自动更新")
    print("=" * 50)
    
    # 启动后台数据更新线程
    update_thread = threading.Thread(target=update_battery_data, daemon=True)
    update_thread.start()
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=False)
