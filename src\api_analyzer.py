#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口分析器
从抓包数据中提取API接口信息和认证机制
"""

import xml.etree.ElementTree as ET
import base64
import json
import urllib.parse
from datetime import datetime

class APIAnalyzer:
    def __init__(self, xml_file_path):
        self.xml_file_path = xml_file_path
        self.api_endpoints = {}
        self.auth_info = {}
        
    def analyze_burp_data(self):
        """分析Burp Suite抓包数据"""
        try:
            tree = ET.parse(self.xml_file_path)
            root = tree.getroot()
            
            for item in root.findall('item'):
                url = item.find('url').text
                method = item.find('method').text
                request_elem = item.find('request')
                
                if request_elem is not None and request_elem.get('base64') == 'true':
                    # 解码请求数据
                    request_data = base64.b64decode(request_elem.text).decode('utf-8')
                    self._extract_api_info(url, method, request_data)
                    
        except Exception as e:
            print(f"分析抓包数据时出错: {e}")
    
    def _extract_api_info(self, url, method, request_data):
        """提取API信息"""
        # 解析URL
        parsed_url = urllib.parse.urlparse(url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
        path = parsed_url.path
        query = parsed_url.query
        
        # 提取请求头
        headers = {}
        lines = request_data.split('\n')
        for line in lines[1:]:  # 跳过第一行请求行
            if line.strip() == '':
                break
            if ':' in line:
                key, value = line.split(':', 1)
                headers[key.strip()] = value.strip()
        
        # 分类API端点
        if '/userBattery/api/list' in path:
            self.api_endpoints['battery_list'] = {
                'url': url,
                'method': method,
                'path': path,
                'query': query,
                'headers': headers,
                'description': '获取用户电池列表'
            }
        elif '/fnjbattery/realTime' in path:
            self.api_endpoints['real_time_data'] = {
                'url': url,
                'method': method,
                'path': path,
                'query': query,
                'headers': headers,
                'description': '获取实时电池数据'
            }
        elif '/deviceParameters' in path:
            self.api_endpoints['device_parameters'] = {
                'url': url,
                'method': method,
                'path': path,
                'query': query,
                'headers': headers,
                'description': '获取设备参数'
            }
        elif '/parseProtectionAndWarning' in path:
            self.api_endpoints['protection_warnings'] = {
                'url': url,
                'method': method,
                'path': path,
                'query': query,
                'headers': headers,
                'description': '解析保护和警告信息'
            }
        
        # 提取认证信息
        if 'X-Access-Token' in headers:
            self.auth_info['access_token'] = headers['X-Access-Token']
        if 'User-Agent' in headers:
            self.auth_info['user_agent'] = headers['User-Agent']
    
    def generate_api_config(self):
        """生成API配置文件"""
        config = {
            'base_url': 'https://sys.wyzxcn.com',
            'endpoints': self.api_endpoints,
            'auth': self.auth_info,
            'common_headers': {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'cross-site',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Accept-Encoding': 'gzip, deflate',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=1, i'
            },
            'extracted_params': {
                'userId': '1933343591078334465',
                'clientId': '380074209785',
                'key': '79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c'
            }
        }
        
        return config
    
    def print_analysis_result(self):
        """打印分析结果"""
        print("="*60)
        print("API接口分析结果")
        print("="*60)
        
        print(f"\n发现的API端点数量: {len(self.api_endpoints)}")
        
        for endpoint_name, endpoint_info in self.api_endpoints.items():
            print(f"\n📡 {endpoint_name.upper()}")
            print(f"   描述: {endpoint_info['description']}")
            print(f"   方法: {endpoint_info['method']}")
            print(f"   路径: {endpoint_info['path']}")
            if endpoint_info['query']:
                print(f"   查询参数: {endpoint_info['query']}")
        
        print(f"\n🔐 认证信息:")
        if 'access_token' in self.auth_info:
            token = self.auth_info['access_token']
            print(f"   Access Token: {token[:20]}...{token[-10:] if len(token) > 30 else token}")
        
        print(f"\n🌐 基础URL: https://sys.wyzxcn.com")
        
        # 保存配置到文件
        config = self.generate_api_config()
        with open('api_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ API配置已保存到: api_config.json")

def main():
    """主函数"""
    analyzer = APIAnalyzer('抓包数据.txt')
    analyzer.analyze_burp_data()
    analyzer.print_analysis_result()

if __name__ == "__main__":
    main()
