#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 快速启动器
Quick Start for Wanyang Battery Monitoring System
"""

import webbrowser
import os
from pathlib import Path

def main():
    """快速启动主程序"""
    print("🔋 万洋锂电池监控系统 - 快速启动")
    print("=" * 50)
    
    # 显示项目信息
    print("📱 正在启动小程序复刻版界面...")
    print("✨ 特性:")
    print("  • 完全复刻小程序UI设计")
    print("  • 美观的现代化界面")
    print("  • 完整的控制功能")
    print("  • 实时数据监控")
    print("  • 响应式设计")
    
    # 启动Web界面
    web_file = Path("web/wanyang_battery_app.html")
    if web_file.exists():
        file_url = f"file:///{web_file.absolute().as_posix()}"
        print(f"\n🌐 打开界面: {file_url}")
        webbrowser.open(file_url)
        print("✅ 界面已在浏览器中打开")
        
        print("\n💡 使用说明:")
        print("  1. 点击底部导航切换页面")
        print("  2. 在控制页面执行控制命令")
        print("  3. 查看电芯页面的电压监控")
        print("  4. 详情页面显示完整参数")
        
        print("\n🎯 其他启动选项:")
        print("  • python start.py - 完整启动器")
        print("  • cd src && python enhanced_battery_monitor.py - 后端监控")
        print("  • cd src && python web_server.py - Web服务器")
        
    else:
        print("❌ 找不到Web界面文件")
        print("请检查 web/wanyang_battery_app.html 是否存在")
    
    print("\n🚀 启动完成！")

if __name__ == "__main__":
    main()
