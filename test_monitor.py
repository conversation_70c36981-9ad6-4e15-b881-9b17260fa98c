#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控程序
验证数据获取和显示功能
"""

import json
from datetime import datetime
from battery_data_fetcher import BatteryDataFetcher

def test_data_fetching():
    """测试数据获取功能"""
    print("🔋 锂电池监控系统测试")
    print("=" * 50)
    
    try:
        # 初始化数据获取器
        print("📡 初始化数据获取器...")
        fetcher = BatteryDataFetcher()
        
        # 测试连接
        print("🔗 测试API连接...")
        if not fetcher.test_connection():
            print("❌ API连接失败")
            return False
        
        print("✅ API连接成功")
        
        # 获取数据
        print("📊 获取电池数据...")
        data = fetcher.get_all_data()
        
        if not data.get('success'):
            print("❌ 数据获取失败")
            return False
        
        print("✅ 数据获取成功")
        
        # 解析和显示数据
        print("\n" + "="*50)
        print("📋 电池状态报告")
        print("="*50)
        
        # 解析实时数据
        real_time = data['real_time_data']
        detail_data = json.loads(real_time['detail'])
        battery_info = json.loads(detail_data['batteryPackageInfo'])
        
        # 基本信息
        print(f"🆔 电池ID: {detail_data['batteryId']}")
        print(f"📅 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 主要参数
        soc = float(battery_info['soc'])
        voltage = float(battery_info['totalVoltage'])
        current = float(battery_info['totalCurrent'])
        temperature = float(battery_info['BMSTemp'])
        
        print("📊 主要参数:")
        print(f"  🔋 SOC (剩余电量): {soc:.1f}%")
        print(f"  ⚡ 总电压: {voltage:.2f}V")
        print(f"  🔌 总电流: {current:.2f}A")
        print(f"  🌡️ BMS温度: {temperature:.1f}°C")
        print(f"  📦 剩余容量: {battery_info['residualCapacity']}Ah")
        print(f"  🔄 循环次数: {battery_info['loopTimes']}次")
        print()
        
        # 充电状态
        if current < 0:
            status = "🔌 充电中"
        elif current > 0:
            status = "🔋 放电中"
        else:
            status = "⏸️ 静置"
        print(f"🔄 状态: {status}")
        print()
        
        # 电芯信息
        cell_voltages = [float(v) for v in battery_info['cellVoltageDetail']]
        print("🔬 电芯信息:")
        print(f"  📊 电芯数量: {len(cell_voltages)}节")
        print(f"  📈 最高电压: {max(cell_voltages):.3f}V")
        print(f"  📉 最低电压: {min(cell_voltages):.3f}V")
        print(f"  📊 平均电压: {sum(cell_voltages)/len(cell_voltages):.3f}V")
        print(f"  📏 电压差: {max(cell_voltages) - min(cell_voltages):.3f}V")
        print()
        
        # 位置信息
        print("📍 位置信息:")
        print(f"  🌍 纬度: {detail_data['latitude']}")
        print(f"  🌍 经度: {detail_data['longitude']}")
        print(f"  🏔️ 海拔: {detail_data['altitude']}m")
        print(f"  🚗 速度: {detail_data['speed']}km/h")
        print()
        
        # 告警信息
        alerts = []
        protections = []
        if data.get('protection_warnings'):
            result = data['protection_warnings']['result']
            alerts = result.get('alerts', [])
            protections = result.get('protections', [])
        
        print("⚠️ 系统状态:")
        if protections:
            print("  🚨 保护触发:")
            for protection in protections:
                print(f"    - {protection}")
        
        if alerts:
            print("  ⚡ 系统提示:")
            for alert in alerts:
                print(f"    - {alert}")
        
        if not protections and not alerts:
            print("  ✅ 系统正常，无异常告警")
        
        print()
        print("="*50)
        print("✅ 测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_simple_html_report(data):
    """创建简单的HTML报告"""
    if not data.get('success'):
        return None
    
    # 解析数据
    real_time = data['real_time_data']
    detail_data = json.loads(real_time['detail'])
    battery_info = json.loads(detail_data['batteryPackageInfo'])
    
    # 获取告警信息
    alerts = []
    protections = []
    if data.get('protection_warnings'):
        result = data['protection_warnings']['result']
        alerts = result.get('alerts', [])
        protections = result.get('protections', [])
    
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电池状态报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }}
        .metric {{ display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center; min-width: 120px; }}
        .metric-value {{ font-size: 1.5em; font-weight: bold; color: #27ae60; }}
        .metric-label {{ color: #7f8c8d; font-size: 0.9em; }}
        .status {{ padding: 15px; margin: 20px 0; border-radius: 8px; }}
        .status-normal {{ background: #d4edda; border-left: 4px solid #28a745; color: #155724; }}
        .status-warning {{ background: #fff3cd; border-left: 4px solid #ffc107; color: #856404; }}
        .timestamp {{ text-align: center; color: #6c757d; margin-top: 30px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔋 锂电池状态报告</h1>
            <p>电池ID: {detail_data['batteryId']}</p>
        </div>
        
        <div class="metric">
            <div class="metric-value">{battery_info['soc']}%</div>
            <div class="metric-label">剩余电量</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">{battery_info['totalVoltage']}V</div>
            <div class="metric-label">总电压</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">{abs(float(battery_info['totalCurrent'])):.1f}A</div>
            <div class="metric-label">电流</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">{battery_info['BMSTemp']}°C</div>
            <div class="metric-label">温度</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">{battery_info['residualCapacity']}Ah</div>
            <div class="metric-label">剩余容量</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">{battery_info['loopTimes']}</div>
            <div class="metric-label">循环次数</div>
        </div>
        
        <div class="status {'status-normal' if not protections else 'status-warning'}">
            <strong>系统状态:</strong>
            {'✅ 系统正常运行' if not protections and not alerts else ''}
            {''.join([f'<br>🚨 {p}' for p in protections])}
            {''.join([f'<br>⚡ {a}' for a in alerts])}
        </div>
        
        <div class="timestamp">
            生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        </div>
    </div>
    
    <script>
        // 自动刷新页面
        setTimeout(function() {{
            location.reload();
        }}, 30000); // 30秒后刷新
    </script>
</body>
</html>
"""
    
    return html_content

def main():
    """主函数"""
    print("🚀 启动电池监控测试...")
    
    # 测试数据获取
    if test_data_fetching():
        print("\n📝 生成HTML报告...")
        
        try:
            # 重新获取数据用于HTML报告
            fetcher = BatteryDataFetcher()
            data = fetcher.get_all_data()
            
            if data.get('success'):
                html_content = create_simple_html_report(data)
                
                if html_content:
                    # 保存HTML文件
                    filename = f"battery_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    
                    print(f"✅ HTML报告已生成: {filename}")
                    print(f"🌐 可以在浏览器中打开查看")
                    
                    # 尝试打开浏览器
                    import webbrowser
                    import os
                    file_path = os.path.abspath(filename)
                    webbrowser.open(f'file://{file_path}')
                    print(f"🚀 已尝试在浏览器中打开报告")
                else:
                    print("❌ HTML报告生成失败")
            else:
                print("❌ 无法获取数据生成报告")
                
        except Exception as e:
            print(f"❌ 生成HTML报告时出错: {e}")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    main()
