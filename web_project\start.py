#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 启动脚本
Wanyang Battery Monitoring System - Startup Script
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    try:
        import flask
        print("✅ Flask 已安装")
        return True
    except ImportError:
        print("❌ Flask 未安装")
        print("💡 正在安装依赖...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖安装失败")
            return False

def get_local_ip():
    """获取本机IP地址"""
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "localhost"

def main():
    """主函数"""
    print("🔋 万洋锂电池监控系统 - Web版本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        return
    
    # 获取IP地址
    local_ip = get_local_ip()
    
    print("🌐 准备启动Web服务器...")
    print("📱 访问地址:")
    print(f"   • 本机访问: http://localhost:5000")
    print(f"   • 手机访问: http://{local_ip}:5000")
    print("🔄 数据每3秒自动更新")
    print("🎮 支持远程控制功能")
    print("=" * 50)
    
    # 询问是否自动打开浏览器
    try:
        choice = input("是否自动打开浏览器? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '']:
            print("🌐 3秒后自动打开浏览器...")
            time.sleep(3)
            webbrowser.open("http://localhost:5000")
    except KeyboardInterrupt:
        print("\n👋 程序已取消")
        return
    
    # 启动Flask应用
    try:
        print("🚀 启动Web服务器...")
        from app import app
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
