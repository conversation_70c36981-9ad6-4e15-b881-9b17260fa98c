<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔋 锂电池实时监控</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: #f8f9ff;
            min-height: 100vh;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 25px 25px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .battery-icon {
            width: 24px;
            height: 16px;
            border: 2px solid white;
            border-radius: 2px;
            position: relative;
            display: inline-block;
        }
        
        .battery-icon::after {
            content: '';
            position: absolute;
            right: -4px;
            top: 4px;
            width: 2px;
            height: 6px;
            background: white;
            border-radius: 0 1px 1px 0;
        }
        
        .control-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .refresh-btn {
            background: #4285f4;
            color: white;
        }
        
        .auto-refresh-btn {
            background: #34a853;
            color: white;
        }
        
        .control-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .main-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            padding: 20px;
            margin-bottom: 10px;
        }
        
        .stat-card {
            background: white;
            padding: 16px 12px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e8eaed;
        }
        
        .stat-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #34a853;
        }
        
        .secondary-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            padding: 0 20px;
            margin-bottom: 20px;
        }
        
        .voltage-section {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .voltage-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
        }
        
        .cell-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 8px 4px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .cell-item.high {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .cell-item.low {
            background: #cce5ff;
            border-color: #007bff;
            color: #004085;
        }
        
        .cell-number {
            font-size: 9px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .cell-voltage {
            font-size: 10px;
            font-weight: bold;
        }
        
        .status-section {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 12px;
        }
        
        .status-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #34a853;
        }
        
        .status-icon.warning {
            background: #fbbc04;
        }
        
        .device-info {
            background: white;
            margin: 0 20px 80px;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            text-align: center;
        }
        
        .device-code {
            font-size: 11px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .update-time {
            font-size: 10px;
            color: #999;
        }
        
        .floating-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        
        .floating-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            transition: all 0.3s;
        }
        
        .floating-btn.primary {
            background: linear-gradient(135deg, #4285f4, #1a73e8);
        }
        
        .floating-btn.success {
            background: linear-gradient(135deg, #34a853, #137333);
        }
        
        .floating-btn.warning {
            background: linear-gradient(135deg, #fbbc04, #f9ab00);
        }
        
        .floating-btn.danger {
            background: linear-gradient(135deg, #ea4335, #d33b2c);
        }
        
        .floating-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
        }
        
        .toast {
            position: fixed;
            top: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(52, 168, 83, 0.95);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 9999;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            opacity: 0;
            transition: all 0.3s;
        }
        
        .toast.show {
            opacity: 1;
        }
        
        .auto-refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(52, 168, 83, 0.9);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 500;
            z-index: 1000;
        }
        
        .auto-refresh-indicator.refreshing {
            background: rgba(251, 188, 4, 0.9);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 自动刷新指示器 -->
        <div class="auto-refresh-indicator" id="refreshIndicator">已连接</div>

        <!-- 头部 -->
        <div class="header">
            <h1>
                <div class="battery-icon"></div>
                锂电池实时监控
            </h1>
            <div class="control-buttons">
                <button class="control-btn refresh-btn" onclick="refreshData()">刷新数据</button>
                <button class="control-btn auto-refresh-btn" onclick="toggleAutoRefresh()">开启自动刷新</button>
            </div>
        </div>

        <!-- 主要统计数据 -->
        <div class="main-stats">
            <div class="stat-card">
                <div class="stat-label">剩余电量</div>
                <div class="stat-value" id="soc">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">总电压</div>
                <div class="stat-value" id="totalVoltage">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">电流</div>
                <div class="stat-value" id="totalCurrent">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">温度</div>
                <div class="stat-value" id="temperature">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">剩余容量</div>
                <div class="stat-value" id="remainingCapacity">--</div>
            </div>
        </div>

        <!-- 次要统计数据 -->
        <div class="secondary-stats">
            <div class="stat-card">
                <div class="stat-label">循环次数</div>
                <div class="stat-value" id="cycleCount">--</div>
            </div>
        </div>

        <!-- 电芯电压分布 -->
        <div class="voltage-section">
            <div class="section-title">
                ⚡ 电芯电压分布
            </div>
            <div class="voltage-grid" id="voltageGrid">
                <!-- 电芯电压将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="status-section">
            <div class="section-title">
                🔧 系统状态
            </div>
            <div id="statusList">
                <!-- 状态列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 设备信息 -->
        <div class="device-info">
            <div class="device-code" id="deviceCode">电池ID: --</div>
            <div class="update-time" id="updateTime">更新时间: --</div>
        </div>

        <!-- 浮动控制按钮 -->
        <div class="floating-controls">
            <button class="floating-btn primary" onclick="sendCommand('reset_terminal')" title="终端复位">🔄</button>
            <button class="floating-btn success" onclick="sendCommand('allow_charge')" title="允许充电">🔌</button>
            <button class="floating-btn warning" onclick="sendCommand('buzzer_on')" title="开启蜂鸣">🔊</button>
            <button class="floating-btn danger" onclick="sendCommand('forbid_discharge')" title="禁止放电">⚡</button>
        </div>

        <!-- 消息提示 -->
        <div class="toast" id="toast"></div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefreshing = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            startAutoRefresh();
        });

        // 刷新数据
        function refreshData() {
            const indicator = document.getElementById('refreshIndicator');
            indicator.textContent = '刷新中...';
            indicator.classList.add('refreshing');

            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    updateDisplay(data);
                    indicator.textContent = '已连接';
                    indicator.classList.remove('refreshing');
                })
                .catch(error => {
                    console.error('获取数据失败:', error);
                    showToast('数据获取失败', 'error');
                    indicator.textContent = '连接失败';
                    indicator.classList.remove('refreshing');
                });
        }

        // 更新显示数据
        function updateDisplay(data) {
            // 更新主要统计数据
            document.getElementById('soc').textContent = data.soc + '%';
            document.getElementById('totalVoltage').textContent = data.total_voltage.toFixed(1) + 'V';
            document.getElementById('totalCurrent').textContent = data.total_current.toFixed(0) + 'A';
            document.getElementById('temperature').textContent = data.mos_temp + '°C';
            document.getElementById('remainingCapacity').textContent = data.remaining_capacity + 'Ah';

            // 更新次要统计数据
            document.getElementById('cycleCount').textContent = data.cycle_count + '次';

            // 更新设备信息
            document.getElementById('deviceCode').textContent = '电池ID: ' + data.battery_code;
            document.getElementById('updateTime').textContent = '更新时间: ' + data.update_time;

            // 更新电芯电压
            updateVoltageGrid(data.cell_voltages);

            // 更新系统状态
            updateStatusList(data.protection_status);
        }

        // 更新电芯电压网格
        function updateVoltageGrid(cellVoltages) {
            const grid = document.getElementById('voltageGrid');
            grid.innerHTML = '';

            const maxVoltage = Math.max(...cellVoltages);
            const minVoltage = Math.min(...cellVoltages);

            cellVoltages.forEach((voltage, index) => {
                const cellDiv = document.createElement('div');
                cellDiv.className = 'cell-item';

                // 标记最高最低电压
                if (Math.abs(voltage - maxVoltage) < 0.001) {
                    cellDiv.classList.add('high');
                } else if (Math.abs(voltage - minVoltage) < 0.001) {
                    cellDiv.classList.add('low');
                }

                cellDiv.innerHTML = `
                    <div class="cell-number">电芯${index + 1}</div>
                    <div class="cell-voltage">${voltage.toFixed(3)}V</div>
                `;

                grid.appendChild(cellDiv);
            });
        }

        // 更新系统状态列表
        function updateStatusList(protectionStatus) {
            const statusList = document.getElementById('statusList');
            statusList.innerHTML = '';

            for (const [key, value] of Object.entries(protectionStatus)) {
                const statusDiv = document.createElement('div');
                statusDiv.className = 'status-item';

                const iconClass = value ? '' : 'warning';
                const statusText = value ? '正常' : '异常';

                statusDiv.innerHTML = `
                    <div class="status-icon ${iconClass}"></div>
                    <span>${key}: ${statusText}</span>
                `;

                statusList.appendChild(statusDiv);
            }
        }

        // 开启/关闭自动刷新
        function toggleAutoRefresh() {
            if (isAutoRefreshing) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        }

        // 开始自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }

            autoRefreshInterval = setInterval(refreshData, 3000); // 每3秒刷新
            isAutoRefreshing = true;

            const btn = document.querySelector('.auto-refresh-btn');
            btn.textContent = '关闭自动刷新';
            btn.style.background = '#ea4335';

            showToast('自动刷新已开启');
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }

            isAutoRefreshing = false;

            const btn = document.querySelector('.auto-refresh-btn');
            btn.textContent = '开启自动刷新';
            btn.style.background = '#34a853';

            showToast('自动刷新已关闭');
        }

        // 发送控制命令
        function sendCommand(command) {
            const button = event.target;
            const originalContent = button.innerHTML;

            button.disabled = true;
            button.innerHTML = '⏳';
            button.style.opacity = '0.6';

            fetch('/api/command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ command: command })
            })
            .then(response => response.json())
            .then(data => {
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.opacity = '1';

                if (data.success) {
                    showToast(data.message);
                } else {
                    showToast(data.message, 'error');
                }
            })
            .catch(error => {
                button.disabled = false;
                button.innerHTML = originalContent;
                button.style.opacity = '1';

                console.error('命令执行失败:', error);
                showToast('命令执行失败', 'error');
            });
        }

        // 显示消息提示
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;

            if (type === 'error') {
                toast.style.background = 'rgba(234, 67, 53, 0.95)';
            } else {
                toast.style.background = 'rgba(52, 168, 83, 0.95)';
            }

            toast.classList.add('show');

            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
