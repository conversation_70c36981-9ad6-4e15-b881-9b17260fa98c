<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>锂电池实时监控系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .status-bar {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 10px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
        }
        
        .status-indicator.connected {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .status-indicator.monitoring {
            background: #cce7ff;
            border-color: #b3d9ff;
            color: #004085;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #6c757d;
        }
        
        .status-dot.green {
            background: #28a745;
        }
        
        .status-dot.blue {
            background: #007bff;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 10px;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-bottom: 8px;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .metric-unit {
            font-size: 0.8em;
            color: #7f8c8d;
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        .cell-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 8px;
            margin: 20px 0;
        }
        
        .cell-item {
            background: #e8f5e8;
            padding: 8px;
            text-align: center;
            border-radius: 5px;
            font-size: 0.8em;
            border: 2px solid #27ae60;
            transition: all 0.3s ease;
        }
        
        .cell-item.high {
            background: #ffe8e8;
            border-color: #e74c3c;
        }
        
        .cell-item.low {
            background: #e8f4fd;
            border-color: #3498db;
        }
        
        .alerts {
            margin-top: 20px;
        }
        
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .timestamp {
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .cell-grid {
                grid-template-columns: repeat(5, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔋 锂电池实时监控系统</h1>
        <div class="status-bar">
            <div class="status-indicator" id="connectionStatus">
                <div class="status-dot" id="connectionDot"></div>
                <span>连接状态: <span id="connectionText">未连接</span></span>
            </div>
            <div class="status-indicator" id="monitoringStatus">
                <div class="status-dot" id="monitoringDot"></div>
                <span>监控状态: <span id="monitoringText">已停止</span></span>
            </div>
        </div>
        <div class="controls">
            <button class="btn btn-primary" onclick="startMonitoring()">开始监控</button>
            <button class="btn btn-danger" onclick="stopMonitoring()">停止监控</button>
        </div>
    </div>
    
    <div class="container">
        <div class="dashboard">
            <!-- 实时数据卡片 -->
            <div class="card">
                <div class="card-title">实时数据</div>
                <div class="metrics-grid">
                    <div class="metric">
                        <div class="metric-label">剩余电量</div>
                        <div class="metric-value" id="soc">--<span class="metric-unit">%</span></div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">总电压</div>
                        <div class="metric-value" id="voltage">--<span class="metric-unit">V</span></div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">电流</div>
                        <div class="metric-value" id="current">--<span class="metric-unit">A</span></div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">温度</div>
                        <div class="metric-value" id="temperature">--<span class="metric-unit">°C</span></div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">剩余容量</div>
                        <div class="metric-value" id="capacity">--<span class="metric-unit">Ah</span></div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">循环次数</div>
                        <div class="metric-value" id="cycles">--<span class="metric-unit">次</span></div>
                    </div>
                </div>
            </div>
            
            <!-- 趋势图表 -->
            <div class="card">
                <div class="card-title">数据趋势</div>
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 电芯状态 -->
        <div class="card">
            <div class="card-title">电芯电压分布</div>
            <div class="cell-grid" id="cellGrid">
                <!-- 电芯数据将通过JavaScript动态生成 -->
            </div>
            <div class="chart-container">
                <canvas id="cellChart"></canvas>
            </div>
        </div>
        
        <!-- 告警信息 -->
        <div class="card">
            <div class="card-title">系统状态</div>
            <div class="alerts" id="alertsContainer">
                <div class="alert alert-success">
                    <strong>系统正常</strong> - 等待数据连接...
                </div>
            </div>
        </div>
        
        <div class="timestamp" id="lastUpdate">
            最后更新: --
        </div>
    </div>
    
    <script>
        // WebSocket连接
        const socket = io();
        
        // 图表对象
        let trendChart = null;
        let cellChart = null;
        
        // 数据存储
        let dataHistory = [];
        const maxDataPoints = 50;
        
        // 连接状态管理
        socket.on('connect', function() {
            updateConnectionStatus(true);
            socket.emit('request_latest_data');
        });
        
        socket.on('disconnect', function() {
            updateConnectionStatus(false);
        });
        
        // 接收电池数据
        socket.on('battery_data', function(data) {
            updateBatteryData(data);
            addToHistory(data);
            updateCharts();
        });
        
        // 接收告警信息
        socket.on('battery_alert', function(alert) {
            addAlert(alert);
        });
        
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            const dotEl = document.getElementById('connectionDot');
            const textEl = document.getElementById('connectionText');
            
            if (connected) {
                statusEl.classList.add('connected');
                dotEl.classList.add('green');
                textEl.textContent = '已连接';
            } else {
                statusEl.classList.remove('connected');
                dotEl.classList.remove('green');
                textEl.textContent = '未连接';
            }
        }
        
        function updateMonitoringStatus(monitoring) {
            const statusEl = document.getElementById('monitoringStatus');
            const dotEl = document.getElementById('monitoringDot');
            const textEl = document.getElementById('monitoringText');
            
            if (monitoring) {
                statusEl.classList.add('monitoring');
                dotEl.classList.add('blue');
                textEl.textContent = '监控中';
            } else {
                statusEl.classList.remove('monitoring');
                dotEl.classList.remove('blue');
                textEl.textContent = '已停止';
            }
        }
        
        function updateBatteryData(data) {
            // 更新实时数据显示
            document.getElementById('soc').innerHTML = `${data.soc}<span class="metric-unit">%</span>`;
            document.getElementById('voltage').innerHTML = `${data.voltage}<span class="metric-unit">V</span>`;
            document.getElementById('current').innerHTML = `${data.current}<span class="metric-unit">A</span>`;
            document.getElementById('temperature').innerHTML = `${data.temperature}<span class="metric-unit">°C</span>`;
            document.getElementById('capacity').innerHTML = `${data.remaining_capacity}<span class="metric-unit">Ah</span>`;
            document.getElementById('cycles').innerHTML = `${data.cycle_count}<span class="metric-unit">次</span>`;
            
            // 更新电芯显示
            updateCellDisplay(data.cell_voltages);
            
            // 更新时间戳
            const timestamp = new Date(data.timestamp).toLocaleString('zh-CN');
            document.getElementById('lastUpdate').textContent = `最后更新: ${timestamp}`;
            
            // 更新告警状态
            updateAlerts(data.alerts, data.protections);
        }
        
        function updateCellDisplay(cellVoltages) {
            const cellGrid = document.getElementById('cellGrid');
            cellGrid.innerHTML = '';
            
            if (!cellVoltages || cellVoltages.length === 0) return;
            
            const avgVoltage = cellVoltages.reduce((a, b) => a + b, 0) / cellVoltages.length;
            
            cellVoltages.forEach((voltage, index) => {
                const cellDiv = document.createElement('div');
                cellDiv.className = 'cell-item';
                
                if (voltage > avgVoltage + 0.01) {
                    cellDiv.classList.add('high');
                } else if (voltage < avgVoltage - 0.01) {
                    cellDiv.classList.add('low');
                }
                
                cellDiv.innerHTML = `电芯${index + 1}<br>${voltage}V`;
                cellGrid.appendChild(cellDiv);
            });
        }
        
        function updateAlerts(alerts, protections) {
            const container = document.getElementById('alertsContainer');
            container.innerHTML = '';
            
            // 显示保护状态
            if (protections && protections.length > 0) {
                protections.forEach(protection => {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger';
                    alertDiv.innerHTML = `<strong>保护触发</strong> - ${protection}`;
                    container.appendChild(alertDiv);
                });
            }
            
            // 显示告警信息
            if (alerts && alerts.length > 0) {
                alerts.forEach(alert => {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-warning';
                    alertDiv.innerHTML = `<strong>系统提示</strong> - ${alert}`;
                    container.appendChild(alertDiv);
                });
            }
            
            // 如果没有告警，显示正常状态
            if ((!alerts || alerts.length === 0) && (!protections || protections.length === 0)) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success';
                alertDiv.innerHTML = '<strong>系统正常</strong> - 所有参数正常，无异常告警';
                container.appendChild(alertDiv);
            }
        }
        
        function addToHistory(data) {
            dataHistory.push({
                timestamp: new Date(data.timestamp),
                soc: data.soc,
                voltage: data.voltage,
                current: data.current,
                temperature: data.temperature
            });
            
            if (dataHistory.length > maxDataPoints) {
                dataHistory.shift();
            }
        }
        
        function updateCharts() {
            updateTrendChart();
            updateCellChart();
        }
        
        function updateTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            
            if (trendChart) {
                trendChart.destroy();
            }
            
            const labels = dataHistory.map(d => d.timestamp.toLocaleTimeString('zh-CN'));
            
            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'SOC (%)',
                        data: dataHistory.map(d => d.soc),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y'
                    }, {
                        label: '电压 (V)',
                        data: dataHistory.map(d => d.voltage),
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'SOC (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '电压 (V)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
        
        function updateCellChart() {
            // 获取最新的电芯数据
            if (dataHistory.length === 0) return;
            
            const latestData = dataHistory[dataHistory.length - 1];
            // 这里需要从最新数据中获取电芯电压，暂时使用模拟数据
        }
        
        function startMonitoring() {
            fetch('/api/start_monitoring')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateMonitoringStatus(true);
                        addAlert({
                            type: 'info',
                            message: '监控已开始',
                            severity: 'info'
                        });
                    }
                });
        }
        
        function stopMonitoring() {
            fetch('/api/stop_monitoring')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateMonitoringStatus(false);
                        addAlert({
                            type: 'info',
                            message: '监控已停止',
                            severity: 'info'
                        });
                    }
                });
        }
        
        function addAlert(alert) {
            console.log('Alert:', alert);
        }
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            updateTrendChart();
        });
    </script>
</body>
</html>
