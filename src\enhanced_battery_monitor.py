#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版万洋锂电池监控系统
基于新抓包数据的完整实现
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
import sqlite3
import threading
from flask import Flask, render_template, jsonify, request
import xml.etree.ElementTree as ET
import base64
import urllib.parse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_battery_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedBatteryMonitor:
    """增强版电池监控器"""
    
    def __init__(self):
        self.base_url = "https://sys.wyzxcn.com/jeecg-boot"
        self.user_id = "1933343591078334465"
        self.client_id = "380074209785"
        self.key = "79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c"
        self.access_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI"
        
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x67001434) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309c33)XWEB/13639',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'X-Access-Token': self.access_token,
            'Xweb_xhr': '1'
        }
        
        self.battery_data = {}
        self.running = False
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect('enhanced_battery_monitor.db')
            cursor = conn.cursor()
            
            # 创建电池数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS battery_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    battery_id TEXT,
                    soc INTEGER,
                    total_voltage REAL,
                    total_current REAL,
                    mos_temp INTEGER,
                    env_temp INTEGER,
                    cell_temp TEXT,
                    total_mileage REAL,
                    speed REAL,
                    loop_times INTEGER,
                    avg_voltage REAL,
                    max_voltage REAL,
                    min_voltage REAL,
                    cell_voltages TEXT,
                    alerts TEXT,
                    protections TEXT,
                    latitude REAL,
                    longitude REAL,
                    gsm_signal INTEGER
                )
            ''')
            
            # 创建控制命令表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS control_commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    command_type TEXT,
                    command_data TEXT,
                    status TEXT,
                    response TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
    
    def get_battery_list(self) -> List[Dict]:
        """获取电池列表"""
        try:
            url = f"{self.base_url}/battery/userBattery/api/list"
            params = {'userId': self.user_id}
            
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                return data.get('result', [])
            else:
                logger.error(f"获取电池列表失败: {data.get('message')}")
                return []
                
        except Exception as e:
            logger.error(f"获取电池列表异常: {e}")
            return []
    
    def get_real_time_data(self) -> Optional[Dict]:
        """获取实时数据"""
        try:
            url = f"{self.base_url}/fnjbattery/realTime"
            params = {
                'key': self.key,
                'clientId': self.client_id
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == 200:
                detail_str = data.get('detail', '{}')
                detail_data = json.loads(detail_str)
                return self.parse_real_time_data(detail_data)
            else:
                logger.error(f"获取实时数据失败: {data.get('message')}")
                return None
                
        except Exception as e:
            logger.error(f"获取实时数据异常: {e}")
            return None
    
    def parse_real_time_data(self, detail_data: Dict) -> Dict:
        """解析实时数据"""
        try:
            # 解析电池包信息
            battery_package_info = json.loads(detail_data.get('batteryPackageInfo', '{}'))
            
            # 解析BMS状态信息
            bms_status_info = json.loads(detail_data.get('bmsStatusInfo', '{}'))
            
            # 解析基站信息
            base_station_info = json.loads(detail_data.get('baseStationInfo', '{}'))
            
            parsed_data = {
                # 基本信息
                'battery_id': battery_package_info.get('batteryId', ''),
                'update_time': detail_data.get('time', ''),
                'latitude': float(detail_data.get('latitude', 0)),
                'longitude': float(detail_data.get('longitude', 0)),
                'speed': float(detail_data.get('speed', 0)),
                
                # 电池包信息
                'soc': int(battery_package_info.get('soc', 0)),
                'total_voltage': float(battery_package_info.get('totalVoltage', 0)),
                'total_current': float(battery_package_info.get('totalCurrent', 0)),
                'residual_capacity': float(battery_package_info.get('residualCapacity', 0)),
                'current_capacity': float(battery_package_info.get('currentCapacity', 0)),
                'loop_times': int(battery_package_info.get('loopTimes', 0)),
                
                # 电芯信息
                'cell_quantity': int(battery_package_info.get('cellQuantity', 0)),
                'cell_voltages': [float(v) for v in battery_package_info.get('cellVoltageDetail', [])],
                
                # 温度信息
                'bms_temp': int(battery_package_info.get('BMSTemp', 0)),
                'temp_detail': [float(t) for t in battery_package_info.get('tempDetailInfo', [])],
                'env_temp': int(bms_status_info.get('envTemp', 0)),
                
                # 状态信息
                'charge_mos_status': battery_package_info.get('batteryStatus', {}).get('chargeMOSStatus', '0') == '1',
                'discharge_mos_status': battery_package_info.get('batteryStatus', {}).get('disChargeMOSStatus', '0') == '1',
                'charge_status': battery_package_info.get('batteryStatus', {}).get('charge', '0') == '1',
                'discharge_status': battery_package_info.get('batteryStatus', {}).get('discharge', '0') == '1',
                
                # 里程和差值
                'total_mileage': float(bms_status_info.get('totalMileage', 0)),
                'vol_differ': float(bms_status_info.get('volDiffer', 0)),
                
                # GSM信号
                'gsm_signal': int(base_station_info.get('GSMSignalIntensity', 0)),
                'location_sat_num': int(base_station_info.get('locationSatNum', 0)),
                
                # 计算值
                'total_power': float(battery_package_info.get('totalVoltage', 0)) * float(battery_package_info.get('totalCurrent', 0)),
                'avg_voltage': sum([float(v) for v in battery_package_info.get('cellVoltageDetail', [])]) / max(1, len(battery_package_info.get('cellVoltageDetail', []))),
                'max_voltage': max([float(v) for v in battery_package_info.get('cellVoltageDetail', [])]) if battery_package_info.get('cellVoltageDetail') else 0,
                'min_voltage': min([float(v) for v in battery_package_info.get('cellVoltageDetail', [])]) if battery_package_info.get('cellVoltageDetail') else 0,
            }
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"解析实时数据失败: {e}")
            return {}
    
    def get_protection_and_warning(self, bms_status_info: str) -> Dict:
        """获取保护和告警信息"""
        try:
            url = f"{self.base_url}/fnjbattery/parseProtectionAndWarning"
            params = {'bmsStatusInfo': bms_status_info}
            
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                result = data.get('result', {})
                return {
                    'alerts': result.get('alerts', []),
                    'protections': result.get('protections', [])
                }
            else:
                logger.error(f"获取保护告警信息失败: {data.get('message')}")
                return {'alerts': [], 'protections': []}
                
        except Exception as e:
            logger.error(f"获取保护告警信息异常: {e}")
            return {'alerts': [], 'protections': []}
    
    def get_device_parameters(self) -> Dict:
        """获取设备参数"""
        try:
            url = f"{self.base_url}/fnjbattery/deviceParameters"
            params = {
                'clientId': self.client_id,
                'key': self.key
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == 200:
                detail_str = data.get('detail', '{}')
                return json.loads(detail_str)
            else:
                logger.error(f"获取设备参数失败: {data.get('message')}")
                return {}
                
        except Exception as e:
            logger.error(f"获取设备参数异常: {e}")
            return {}
    
    def save_battery_data(self, data: Dict):
        """保存电池数据到数据库"""
        try:
            conn = sqlite3.connect('enhanced_battery_monitor.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO battery_data (
                    battery_id, soc, total_voltage, total_current, mos_temp, env_temp,
                    cell_temp, total_mileage, speed, loop_times, avg_voltage, max_voltage,
                    min_voltage, cell_voltages, alerts, protections, latitude, longitude, gsm_signal
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data.get('battery_id', ''),
                data.get('soc', 0),
                data.get('total_voltage', 0),
                data.get('total_current', 0),
                data.get('bms_temp', 0),
                data.get('env_temp', 0),
                json.dumps(data.get('temp_detail', [])),
                data.get('total_mileage', 0),
                data.get('speed', 0),
                data.get('loop_times', 0),
                data.get('avg_voltage', 0),
                data.get('max_voltage', 0),
                data.get('min_voltage', 0),
                json.dumps(data.get('cell_voltages', [])),
                json.dumps(data.get('alerts', [])),
                json.dumps(data.get('protections', [])),
                data.get('latitude', 0),
                data.get('longitude', 0),
                data.get('gsm_signal', 0)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"保存电池数据失败: {e}")
    
    def collect_data(self):
        """数据采集主循环"""
        logger.info("开始数据采集...")
        
        while self.running:
            try:
                # 获取实时数据
                real_time_data = self.get_real_time_data()
                if real_time_data:
                    self.battery_data.update(real_time_data)
                    
                    # 获取保护告警信息
                    # 这里需要构造bmsStatusInfo参数，从实时数据中提取
                    
                    # 保存到数据库
                    self.save_battery_data(real_time_data)
                    
                    logger.info(f"数据采集成功 - SOC: {real_time_data.get('soc')}%, 电压: {real_time_data.get('total_voltage')}V")
                
                time.sleep(30)  # 30秒采集一次
                
            except Exception as e:
                logger.error(f"数据采集异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试
    
    def start_monitoring(self):
        """开始监控"""
        self.running = True
        self.monitor_thread = threading.Thread(target=self.collect_data)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        logger.info("监控已停止")
    
    def get_latest_data(self) -> Dict:
        """获取最新数据"""
        return self.battery_data.copy()

# Flask Web应用
app = Flask(__name__)
monitor = EnhancedBatteryMonitor()

@app.route('/')
def index():
    """主页"""
    return render_template('enhanced_monitor.html')

@app.route('/api/battery/data')
def get_battery_data():
    """获取电池数据API"""
    return jsonify(monitor.get_latest_data())

@app.route('/api/battery/refresh')
def refresh_battery_data():
    """刷新电池数据API"""
    data = monitor.get_real_time_data()
    if data:
        monitor.battery_data.update(data)
        return jsonify({'success': True, 'data': data})
    else:
        return jsonify({'success': False, 'message': '获取数据失败'})

if __name__ == '__main__':
    # 启动监控
    monitor.start_monitoring()
    
    try:
        # 启动Web服务
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        monitor.stop_monitoring()
        logger.info("程序已退出")
