#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 系统测试
System Test for Wanyang Battery Monitoring
"""

import requests
import json
import time

def test_web_server():
    """测试Web服务器"""
    print("🌐 测试Web服务器...")
    
    try:
        # 测试主页
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Web服务器运行正常")
            print(f"📄 页面大小: {len(response.text)} 字符")
        else:
            print(f"❌ Web服务器响应异常: {response.status_code}")
            
        # 测试API接口
        response = requests.get("http://localhost:5000/api/data", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ API接口正常")
            print(f"🔋 SOC: {data['soc']}%")
            print(f"⚡ 电压: {data['total_voltage']}V")
            print(f"🌡️ 温度: {data['mos_temp']}°C")
        else:
            print(f"❌ API接口异常: {response.status_code}")
            
        # 测试控制命令
        command_data = {"command": "reset"}
        response = requests.post("http://localhost:5000/api/command", 
                               json=command_data, timeout=5)
        if response.status_code == 200:
            result = response.json()
            print("✅ 控制命令接口正常")
            print(f"📝 响应: {result['message']}")
        else:
            print(f"❌ 控制命令接口异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器")
        print("💡 请先启动Web服务器: python mobile_web_server.py")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_system_info():
    """显示系统信息"""
    print("🔋 万洋锂电池监控系统 - 系统测试")
    print("=" * 50)
    print("📱 手机端Web监控:")
    print("   • 地址: http://localhost:5000")
    print("   • 功能: 实时数据显示、远程控制")
    print("   • 特点: 响应式设计、美观界面")
    print()
    print("📊 控制台监控:")
    print("   • 启动: python console_monitor.py")
    print("   • 功能: 文字版数据、API接口数据")
    print("   • 特点: 实时刷新、详细信息")
    print()
    print("🚀 启动方式:")
    print("   • 简单启动: python simple_start.py")
    print("   • Web服务器: python mobile_web_server.py")
    print("   • 控制台: python console_monitor.py")
    print("=" * 50)

def main():
    """主函数"""
    show_system_info()
    print("\n🧪 开始系统测试...")
    test_web_server()
    
    print("\n" + "=" * 50)
    print("✅ 系统测试完成")
    print("💡 如果Web服务器未启动，请运行: python mobile_web_server.py")
    print("📱 然后用手机浏览器访问: http://你的电脑IP:5000")

if __name__ == "__main__":
    main()
