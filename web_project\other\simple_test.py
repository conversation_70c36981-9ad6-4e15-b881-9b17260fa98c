#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_import():
    """测试导入"""
    try:
        from app import app, battery_data
        print("✅ Flask应用导入成功")
        print(f"🔋 电池SOC: {battery_data['soc']}%")
        print(f"⚡ 电压: {battery_data['total_voltage']}V")
        print(f"🔢 电芯数量: {len(battery_data['cell_voltages'])}")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_template():
    """测试模板文件"""
    template_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'web', 'templates', 'index.html')
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print("✅ HTML模板文件存在")
        print(f"📄 模板大小: {len(content)} 字符")
        
        # 检查关键内容
        key_elements = [
            "锂电池实时监控",
            "电芯电压分布", 
            "系统状态",
            "floating-controls"
        ]
        
        for element in key_elements:
            if element in content:
                print(f"✅ 包含: {element}")
            else:
                print(f"❌ 缺少: {element}")
        return True
    else:
        print("❌ HTML模板文件不存在")
        return False

def main():
    """主函数"""
    print("🔋 万洋锂电池监控系统 - 简单测试")
    print("=" * 40)
    
    print("\n📦 测试模块导入...")
    import_ok = test_import()
    
    print("\n📄 测试模板文件...")
    template_ok = test_template()
    
    print("\n" + "=" * 40)
    if import_ok and template_ok:
        print("✅ 基础测试通过")
        print("🚀 可以启动Web应用:")
        print("   python app.py")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
