#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 应用测试
Application Test for Wanyang Battery Monitoring System
"""

import requests
import json
import time
import sys
import os

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_web_app():
    """测试Web应用"""
    base_url = "http://localhost:5000"
    
    print("🔋 万洋锂电池监控系统 - Web应用测试")
    print("=" * 50)
    
    try:
        # 测试主页
        print("🌐 测试主页...")
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ 主页访问正常")
            print(f"📄 页面大小: {len(response.text)} 字符")
            
            # 检查关键内容
            if "锂电池实时监控" in response.text:
                print("✅ 页面标题正确")
            if "电芯电压分布" in response.text:
                print("✅ 电芯监控功能存在")
            if "系统状态" in response.text:
                print("✅ 状态监控功能存在")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            return False
            
        # 测试数据API
        print("\n📊 测试数据API...")
        response = requests.get(f"{base_url}/api/data", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 数据API正常")
            print(f"🔋 SOC: {data.get('soc', 'N/A')}%")
            print(f"⚡ 电压: {data.get('total_voltage', 'N/A')}V")
            print(f"🌡️ 温度: {data.get('mos_temp', 'N/A')}°C")
            print(f"🔢 电芯数量: {len(data.get('cell_voltages', []))}")
        else:
            print(f"❌ 数据API失败: {response.status_code}")
            return False
            
        # 测试控制API
        print("\n🎮 测试控制API...")
        test_commands = [
            "refresh_data",
            "reset_terminal",
            "allow_charge"
        ]
        
        for command in test_commands:
            response = requests.post(
                f"{base_url}/api/command",
                json={"command": command},
                timeout=5
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 命令 '{command}': {result.get('message', 'OK')}")
            else:
                print(f"❌ 命令 '{command}' 失败: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("✅ Web应用测试完成")
        print("📱 手机访问地址: http://你的电脑IP:5000")
        print("🔄 数据每3秒自动更新")
        print("🎮 支持远程控制功能")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器")
        print("💡 请先启动Web服务器:")
        print("   cd web_project")
        print("   python app.py")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_project_info():
    """显示项目信息"""
    print("\n📁 项目结构:")
    print("web_project/")
    print("├── app.py              # Flask主应用")
    print("├── start.py            # 启动脚本")
    print("├── requirements.txt    # 依赖列表")
    print("├── README.md          # 项目说明")
    print("├── web/               # Web相关文件")
    print("│   ├── templates/     # HTML模板")
    print("│   │   └── index.html # 主页面模板")
    print("│   └── static/        # 静态资源")
    print("└── other/             # 其他文件")
    print("    └── test_app.py    # 测试脚本")
    
    print("\n🚀 启动方式:")
    print("1. python start.py     # 使用启动脚本")
    print("2. python app.py       # 直接启动Flask")

def main():
    """主函数"""
    show_project_info()
    
    print("\n🧪 开始Web应用测试...")
    success = test_web_app()
    
    if success:
        print("\n🎉 测试成功！Web应用运行正常")
    else:
        print("\n❌ 测试失败，请检查Web服务器状态")

if __name__ == "__main__":
    main()
