# 🔋 锂电池实时监控系统

基于微信小程序API的锂电池实时监控系统，支持多种监控模式和美观的数据展示。

## ✨ 功能特性

- 🔄 **实时数据获取**: 自动从API接口获取电池数据
- 📊 **多种显示模式**: 终端、Web界面、HTML报告
- 💾 **数据存储**: SQLite数据库存储历史数据
- ⚠️ **智能告警**: 自动检测异常状态并告警
- 📱 **响应式设计**: 支持PC和移动设备访问
- 🎨 **美观界面**: 现代化的UI设计

## 📁 文件结构

```
xdczb/
├── 抓包数据.txt                    # 原始抓包数据
├── api_config.json                 # API配置文件
├── battery_monitor.db              # SQLite数据库
├── battery_monitor.log             # 系统日志
├── 
├── 核心模块/
│   ├── api_analyzer.py             # API接口分析器
│   ├── battery_data_fetcher.py     # 数据获取模块
│   ├── battery_data_processor.py   # 数据处理器
│   └── battery_data_analyzer.py    # 原始数据分析器
├── 
├── 监控程序/
│   ├── battery_monitor_launcher.py # 系统启动器 ⭐
│   ├── real_time_monitor.py        # 终端实时监控
│   ├── simple_web_monitor.py       # Web监控服务器
│   ├── web_server.py               # 完整Web服务器
│   └── test_monitor.py             # 测试监控程序
├── 
├── Web界面/
│   └── templates/
│       └── monitor.html            # Web监控页面模板
├── 
└── 分析报告/
    ├── battery_analysis_summary.md # 数据分析报告
    ├── battery_data_structured.json# 结构化数据
    └── battery_report_*.html       # 生成的HTML报告
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install requests flask flask-socketio
```

### 2. 启动系统

```bash
# 运行系统启动器（推荐）
python battery_monitor_launcher.py
```

### 3. 选择监控模式

启动后会显示菜单，选择合适的监控模式：

1. **📊 单次数据查询** - 获取一次电池数据
2. **🔄 终端实时监控** - 在终端中实时显示数据
3. **🌐 Web界面监控** - 在浏览器中查看实时数据
4. **📝 生成HTML报告** - 生成静态HTML报告
5. **💾 数据库查看** - 查看历史数据记录

## 📊 监控数据

系统监控以下电池参数：

### 主要参数
- 🔋 **SOC (剩余电量)**: 百分比显示
- ⚡ **总电压**: 实时电压值
- 🔌 **总电流**: 充放电电流
- 🌡️ **BMS温度**: 电池管理系统温度
- 📦 **剩余容量**: 可用容量
- 🔄 **循环次数**: 充放电循环计数

### 电芯信息
- 📊 **电芯数量**: 20节串联
- 📈 **电压分布**: 各电芯电压详情
- 📏 **电压差**: 电芯一致性指标
- 🌡️ **温度分布**: 温度传感器数据

### 系统状态
- ✅ **正常状态**: 系统运行正常
- ⚠️ **告警信息**: 系统提示信息
- 🚨 **保护状态**: 保护机制触发情况

### 位置信息
- 📍 **GPS坐标**: 纬度、经度
- 🏔️ **海拔高度**: 当前海拔
- 🚗 **移动速度**: 实时速度

## 🎛️ 使用说明

### 终端监控模式

```bash
python real_time_monitor.py
```

- 实时刷新显示
- 彩色状态条
- 历史趋势显示
- Ctrl+C 停止监控

### Web监控模式

```bash
python simple_web_monitor.py
```

- 访问 http://localhost:5000
- 支持手动/自动刷新
- 响应式设计
- 实时图表显示

### 数据处理模式

```bash
python battery_data_processor.py
```

- 自动数据验证
- 数据库存储
- 异常告警
- 历史数据管理

## 📈 数据分析

### 电池健康评估

系统会自动评估电池健康状况：

- **优秀**: 电芯一致性好，温度正常，无异常告警
- **良好**: 参数正常，偶有轻微告警
- **注意**: 存在需要关注的参数异常
- **警告**: 检测到严重异常，需要立即处理

### 告警机制

- **低电量告警**: SOC < 20%
- **高温告警**: 温度 > 50°C
- **电芯不一致**: 电压差 > 0.1V
- **保护触发**: BMS保护机制激活

## 🔧 配置说明

### API配置 (api_config.json)

```json
{
  "base_url": "https://sys.wyzxcn.com",
  "auth": {
    "access_token": "your_token_here"
  },
  "extracted_params": {
    "userId": "your_user_id",
    "clientId": "your_client_id"
  }
}
```

### 数据验证规则

系统内置数据验证规则，可在 `battery_data_processor.py` 中调整：

```python
validation_rules = {
    'voltage_min': 60.0,      # 最低电压
    'voltage_max': 90.0,      # 最高电压
    'temperature_max': 80.0,  # 最高温度
    'soc_min': 0.0,          # 最低SOC
    'soc_max': 100.0,        # 最高SOC
    # ... 更多规则
}
```

## 📊 数据存储

系统使用SQLite数据库存储数据：

- **battery_status**: 电池状态历史记录
- **alert_history**: 告警历史记录

查看数据库：

```bash
# 通过启动器查看
python battery_monitor_launcher.py
# 选择选项 5

# 或直接查看
python -c "from battery_data_processor import BatteryDataProcessor; p=BatteryDataProcessor(); print(p.get_historical_data(24))"
```

## 🎨 界面截图

### 终端监控界面
```
🔋============================================================🔋
           锂电池实时监控系统
🔋============================================================🔋

📅 更新时间: 2025-06-18 10:55:06
🆔 电池ID: BT2072055010032504140408

📊 主要状态指标:
┌──────────────────────────────────────────────────────────┐
│ 电量(SOC): ██████████████████████████████░░░░░░░░░░ 96.0% │
│ 总电压:  83.70V    总电流: -10.00A    温度: 33.0°C      │
│ 剩余容量: 51.30Ah   循环次数:    1次                     │
└──────────────────────────────────────────────────────────┘

状态: 🔌 充电中
```

### Web监控界面
- 现代化卡片式布局
- 实时数据更新
- 电芯电压可视化
- 响应式设计

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证access_token是否有效
   - 确认API地址正确

2. **数据解析错误**
   - 检查API响应格式是否变化
   - 查看日志文件获取详细错误信息

3. **Web界面无法访问**
   - 确认Flask服务器正常启动
   - 检查端口5000是否被占用
   - 尝试访问 http://127.0.0.1:5000

### 日志查看

```bash
# 查看系统日志
tail -f battery_monitor.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建GitHub Issue
- 发送邮件至项目维护者

---

**🎯 享受实时监控您的锂电池系统！**
