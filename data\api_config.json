{"base_url": "https://sys.wyzxcn.com", "endpoints": {"battery_list": {"url": "https://sys.wyzxcn.com/jeecg-boot/battery/userBattery/api/list?userId=1933343591078334465", "method": "GET", "path": "/jeecg-boot/battery/userBattery/api/list", "query": "userId=1933343591078334465", "headers": {"Host": "sys.wyzxcn.com", "Xweb_xhr": "1", "X-Access-Token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639", "Content-Type": "application/json", "Accept": "*/*", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "https://servicewechat.com/wxdfb13c626b912abf/12/page-frame.html", "Accept-Encoding": "gzip, deflate", "Accept-Language": "zh-CN,zh;q=0.9", "Priority": "u=1, i"}, "description": "获取用户电池列表"}, "real_time_data": {"url": "https://sys.wyzxcn.com/jeecg-boot/fnjbattery/realTime?key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785", "method": "GET", "path": "/jeecg-boot/fnjbattery/realTime", "query": "key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c&clientId=380074209785", "headers": {"Host": "sys.wyzxcn.com", "Xweb_xhr": "1", "X-Access-Token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639", "Content-Type": "application/json", "Accept": "*/*", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "https://servicewechat.com/wxdfb13c626b912abf/12/page-frame.html", "Accept-Encoding": "gzip, deflate", "Accept-Language": "zh-CN,zh;q=0.9", "Priority": "u=1, i"}, "description": "获取实时电池数据"}, "protection_warnings": {"url": "https://sys.wyzxcn.com/jeecg-boot/fnjbattery/parseProtectionAndWarning?bmsStatusInfo=%7B%22infoTime%22%3A%22250618102355%22%2C%22envTemp%22%3A%2234%22%2C%22alertStatusBit%22%3A%220000000000000000%22%2C%22protectStatusBit%22%3A%220000000000000000%22%2C%22chargeMosStatus%22%3A1%2C%22invalidStatusBit%22%3A%2200000000%22%2C%22disChargeMosStatus%22%3A1%2C%22bmsStatusBit%22%3A%2200000000%22%2C%22balanceStatus1Bit%22%3A%220000110110000111%22%2C%22balanceStatus2Bit%22%3A%220000000010101000%22%2C%22averageVol%22%3A%220.0%22%2C%22volDiffer%22%3A%220.835%22%2C%22totalMileage%22%3A%2215.2%22%2C%22comStatusBit%22%3A%2200000011%22%2C%22remoteUpdate%22%3A%220%22%2C%22downProgress%22%3A%220%22%2C%22updateProgress%22%3A%220%22%2C%22codeAsccII%22%3A%222%2C0%2C%2C0%2C111%2C0%22%2C%22permitDischargeDurationCountDown%22%3A%2264241%22%2C%22forbidDischargeDurationCountDown%22%3A%224300%22%2C%22totalSumChargeTimes%22%3A%224250%22%2C%22lastChargeInterval%22%3A%224350%22%2C%22chargeOrDischargeHighTempTimes%22%3A%224250%22%2C%22chargeOrDischargeLowTempTimes%22%3A%22860%22%2C%22forceOverDischargeTimes%22%3A%22840%22%2C%22forceOverschargeTimes%22%3A%223000%22%2C%22forceOverCurrentTimes%22%3A%223150%22%2C%22forceShortCircleTimes%22%3A%222500%22%2C%22lowVolPowerOffTimes%22%3A%222800%22%2C%22exceptionShutDownTimes%22%3A%22600%22%2C%22forceResetTimes%22%3A%229999%22%2C%22totalSumDischargeTime%22%3A%222700%22%2C%22totalSumchargeTime%22%3A%220%22%2C%22CCID%22%3A%2289860406192490031343%22%2C%22IEMI%22%3A%22460042639021943%22%2C%22DTU%22%3A%22BMS_F24S3TC_V3_00_17%22%2C%22BMSSV%22%3A%223023%22%2C%22BMSHV%22%3A%222%22%2C%22protectFlag%22%3A%2200000000000000000000%22%2C%22alertFlag%22%3A%2200008401001000000000%22%7D", "method": "GET", "path": "/jeecg-boot/fnjbattery/parseProtectionAndWarning", "query": "bmsStatusInfo=%7B%22infoTime%22%3A%22250618102355%22%2C%22envTemp%22%3A%2234%22%2C%22alertStatusBit%22%3A%220000000000000000%22%2C%22protectStatusBit%22%3A%220000000000000000%22%2C%22chargeMosStatus%22%3A1%2C%22invalidStatusBit%22%3A%2200000000%22%2C%22disChargeMosStatus%22%3A1%2C%22bmsStatusBit%22%3A%2200000000%22%2C%22balanceStatus1Bit%22%3A%220000110110000111%22%2C%22balanceStatus2Bit%22%3A%220000000010101000%22%2C%22averageVol%22%3A%220.0%22%2C%22volDiffer%22%3A%220.835%22%2C%22totalMileage%22%3A%2215.2%22%2C%22comStatusBit%22%3A%2200000011%22%2C%22remoteUpdate%22%3A%220%22%2C%22downProgress%22%3A%220%22%2C%22updateProgress%22%3A%220%22%2C%22codeAsccII%22%3A%222%2C0%2C%2C0%2C111%2C0%22%2C%22permitDischargeDurationCountDown%22%3A%2264241%22%2C%22forbidDischargeDurationCountDown%22%3A%224300%22%2C%22totalSumChargeTimes%22%3A%224250%22%2C%22lastChargeInterval%22%3A%224350%22%2C%22chargeOrDischargeHighTempTimes%22%3A%224250%22%2C%22chargeOrDischargeLowTempTimes%22%3A%22860%22%2C%22forceOverDischargeTimes%22%3A%22840%22%2C%22forceOverschargeTimes%22%3A%223000%22%2C%22forceOverCurrentTimes%22%3A%223150%22%2C%22forceShortCircleTimes%22%3A%222500%22%2C%22lowVolPowerOffTimes%22%3A%222800%22%2C%22exceptionShutDownTimes%22%3A%22600%22%2C%22forceResetTimes%22%3A%229999%22%2C%22totalSumDischargeTime%22%3A%222700%22%2C%22totalSumchargeTime%22%3A%220%22%2C%22CCID%22%3A%2289860406192490031343%22%2C%22IEMI%22%3A%22460042639021943%22%2C%22DTU%22%3A%22BMS_F24S3TC_V3_00_17%22%2C%22BMSSV%22%3A%223023%22%2C%22BMSHV%22%3A%222%22%2C%22protectFlag%22%3A%2200000000000000000000%22%2C%22alertFlag%22%3A%2200008401001000000000%22%7D", "headers": {"Host": "sys.wyzxcn.com", "Xweb_xhr": "1", "X-Access-Token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639", "Content-Type": "application/json", "Accept": "*/*", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "https://servicewechat.com/wxdfb13c626b912abf/12/page-frame.html", "Accept-Encoding": "gzip, deflate", "Accept-Language": "zh-CN,zh;q=0.9", "Priority": "u=1, i"}, "description": "解析保护和警告信息"}, "device_parameters": {"url": "https://sys.wyzxcn.com/jeecg-boot/fnjbattery/deviceParameters?clientId=380074209785&key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c", "method": "GET", "path": "/jeecg-boot/fnjbattery/deviceParameters", "query": "clientId=380074209785&key=79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c", "headers": {"Host": "sys.wyzxcn.com", "Xweb_xhr": "1", "X-Access-Token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639", "Content-Type": "application/json", "Accept": "*/*", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "https://servicewechat.com/wxdfb13c626b912abf/12/page-frame.html", "Accept-Encoding": "gzip, deflate", "Accept-Language": "zh-CN,zh;q=0.9", "Priority": "u=1, i"}, "description": "获取设备参数"}}, "auth": {"access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NTA1MTI5MDQsInVzZXJuYW1lIjoid2FueWFuZ19aWnhYbHNxQUZnIn0.js9JxnhkSEzdlsn9mXjr6G2cAr_AZDmezNRmpSeKjpI", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c33)XWEB/13639"}, "common_headers": {"Content-Type": "application/json", "Accept": "*/*", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Accept-Encoding": "gzip, deflate", "Accept-Language": "zh-CN,zh;q=0.9", "Priority": "u=1, i"}, "extracted_params": {"userId": "1933343591078334465", "clientId": "380074209785", "key": "79a156a3bf5c66b29f78981ec2ff6206891e29ed45f0633c"}}