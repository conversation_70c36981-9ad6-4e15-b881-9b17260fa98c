#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时电池监控程序
自动获取数据并在终端中美观显示
"""

import time
import json
import os
import sys
from datetime import datetime
from battery_data_fetcher import BatteryDataFetcher
import threading

class RealTimeMonitor:
    def __init__(self):
        """初始化监控器"""
        self.fetcher = BatteryDataFetcher()
        self.running = False
        self.latest_data = None
        self.data_history = []
        self.max_history = 100
        
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def format_status_bar(self, label, value, unit="", width=20):
        """格式化状态条"""
        if isinstance(value, (int, float)):
            if unit == "%":
                # SOC百分比条
                filled = int((value / 100) * width)
                bar = "█" * filled + "░" * (width - filled)
                color = "\033[92m" if value > 50 else "\033[93m" if value > 20 else "\033[91m"
                return f"{label}: {color}{bar}\033[0m {value:.1f}{unit}"
            else:
                return f"{label}: {value:.2f}{unit}"
        else:
            return f"{label}: {value}{unit}"
    
    def display_battery_status(self, data):
        """显示电池状态"""
        if not data or not data.get('success'):
            print("❌ 数据获取失败")
            return
        
        # 解析实时数据
        real_time = data['real_time_data']
        detail_data = json.loads(real_time['detail'])
        battery_info = json.loads(detail_data['batteryPackageInfo'])
        
        # 获取关键参数
        soc = float(battery_info['soc'])
        voltage = float(battery_info['totalVoltage'])
        current = float(battery_info['totalCurrent'])
        temperature = float(battery_info['BMSTemp'])
        remaining_capacity = float(battery_info['residualCapacity'])
        cell_voltages = [float(v) for v in battery_info['cellVoltageDetail']]
        
        # 清屏并显示标题
        self.clear_screen()
        print("🔋" + "="*60 + "🔋")
        print("           锂电池实时监控系统")
        print("🔋" + "="*60 + "🔋")
        print()
        
        # 显示时间戳
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"📅 更新时间: {timestamp}")
        print(f"🆔 电池ID: {detail_data['batteryId']}")
        print()
        
        # 主要状态指标
        print("📊 主要状态指标:")
        print("┌" + "─"*58 + "┐")
        print(f"│ {self.format_status_bar('电量(SOC)', soc, '%', 30):56} │")
        print(f"│ 总电压: {voltage:6.2f}V    总电流: {current:6.2f}A    温度: {temperature:4.1f}°C │")
        print(f"│ 剩余容量: {remaining_capacity:5.2f}Ah   循环次数: {battery_info['loopTimes']:4}次        │")
        print("└" + "─"*58 + "┘")
        print()
        
        # 充电状态
        is_charging = current < 0
        charge_status = "🔌 充电中" if is_charging else "🔋 放电中" if current > 0 else "⏸️ 静置"
        charge_color = "\033[92m" if is_charging else "\033[93m" if current > 0 else "\033[94m"
        print(f"状态: {charge_color}{charge_status}\033[0m")
        print()
        
        # 电芯电压分布
        print("🔬 电芯电压分布:")
        print("┌" + "─"*58 + "┐")
        
        # 计算电芯统计
        max_voltage = max(cell_voltages)
        min_voltage = min(cell_voltages)
        avg_voltage = sum(cell_voltages) / len(cell_voltages)
        voltage_diff = max_voltage - min_voltage
        
        print(f"│ 最高: {max_voltage:.3f}V  最低: {min_voltage:.3f}V  平均: {avg_voltage:.3f}V  差值: {voltage_diff:.3f}V │")
        print("│" + " "*58 + "│")
        
        # 显示电芯电压（每行10个）
        for i in range(0, len(cell_voltages), 10):
            row_voltages = cell_voltages[i:i+10]
            voltage_str = "  ".join([f"{v:.3f}" for v in row_voltages])
            print(f"│ 电芯{i+1:2d}-{min(i+10, len(cell_voltages)):2d}: {voltage_str:<42} │")
        
        print("└" + "─"*58 + "┘")
        print()
        
        # 告警和保护状态
        alerts = []
        protections = []
        if data.get('protection_warnings'):
            result = data['protection_warnings']['result']
            alerts = result.get('alerts', [])
            protections = result.get('protections', [])
        
        print("⚠️ 系统状态:")
        print("┌" + "─"*58 + "┐")
        
        if protections:
            for protection in protections:
                print(f"│ 🚨 保护: {protection:<47} │")
        
        if alerts:
            for alert in alerts[:3]:  # 只显示前3个告警
                print(f"│ ⚡ 提示: {alert:<47} │")
        
        if not protections and not alerts:
            print("│ ✅ 系统正常运行，无异常告警                               │")
        
        print("└" + "─"*58 + "┘")
        print()
        
        # 位置信息
        print(f"📍 位置: 纬度 {detail_data['latitude']}, 经度 {detail_data['longitude']}")
        print(f"🏔️ 海拔: {detail_data['altitude']}m  🚗 速度: {detail_data['speed']}km/h")
        print()
        
        # 数据趋势（如果有历史数据）
        if len(self.data_history) > 1:
            print("📈 数据趋势 (最近5次):")
            print("┌" + "─"*58 + "┐")
            recent_data = self.data_history[-5:]
            for i, hist_data in enumerate(recent_data):
                time_str = hist_data['timestamp'].strftime('%H:%M:%S')
                soc_trend = "📈" if i > 0 and hist_data['soc'] > recent_data[i-1]['soc'] else "📉" if i > 0 and hist_data['soc'] < recent_data[i-1]['soc'] else "➡️"
                print(f"│ {time_str} SOC:{hist_data['soc']:5.1f}% V:{hist_data['voltage']:5.2f}V T:{hist_data['temperature']:4.1f}°C {soc_trend} │")
            print("└" + "─"*58 + "┘")
            print()
        
        # 控制提示
        print("🎮 控制: Ctrl+C 停止监控")
        print("🔄 自动刷新间隔: 30秒")
        
        # 保存历史数据
        self.data_history.append({
            'timestamp': datetime.now(),
            'soc': soc,
            'voltage': voltage,
            'current': current,
            'temperature': temperature
        })
        
        # 限制历史数据长度
        if len(self.data_history) > self.max_history:
            self.data_history.pop(0)
    
    def start_monitoring(self, interval=30):
        """开始监控"""
        print("🚀 启动电池实时监控...")
        print("📡 测试API连接...")
        
        # 测试连接
        if not self.fetcher.test_connection():
            print("❌ API连接失败，请检查网络和配置")
            return
        
        print("✅ API连接成功")
        print("🔄 开始实时监控...")
        time.sleep(2)
        
        self.running = True
        
        try:
            while self.running:
                # 获取数据
                data = self.fetcher.get_all_data()
                
                # 显示数据
                self.display_battery_status(data)
                
                # 等待下次更新
                for i in range(interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
        except KeyboardInterrupt:
            print("\n\n🛑 用户停止监控")
        except Exception as e:
            print(f"\n\n❌ 监控过程中出错: {e}")
        finally:
            self.running = False
            print("👋 监控已停止")
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False

def main():
    """主函数"""
    monitor = RealTimeMonitor()
    
    print("🔋 锂电池实时监控系统")
    print("=" * 40)
    print("1. 开始监控")
    print("2. 单次查询")
    print("3. 退出")
    print("=" * 40)
    
    while True:
        try:
            choice = input("请选择操作 (1-3): ").strip()
            
            if choice == '1':
                interval = input("请输入刷新间隔(秒，默认30): ").strip()
                interval = int(interval) if interval.isdigit() else 30
                monitor.start_monitoring(interval)
                break
            elif choice == '2':
                print("📡 获取单次数据...")
                data = monitor.fetcher.get_all_data()
                monitor.display_battery_status(data)
                input("\n按回车键继续...")
            elif choice == '3':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

if __name__ == "__main__":
    main()
