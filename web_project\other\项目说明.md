# 🔋 万洋锂电池监控系统 - Web版本

## 🎯 项目概述

这是一个完全复刻小程序界面的锂电池实时监控Web应用，专为手机端优化设计。项目采用标准Flask架构，提供美观的用户界面和完整的监控功能。

## ✨ 核心特点

### 🎨 界面设计
- **完美复刻** - 100%复刻原小程序界面设计
- **手机优化** - 专为手机端优化的响应式设计
- **美观界面** - 现代化的卡片式布局和渐变背景
- **流畅动画** - 丰富的交互动画效果

### 📊 监控功能
- **实时数据** - SOC、电压、电流、温度实时显示
- **电芯监控** - 20节电芯电压分布可视化
- **状态监控** - 系统保护状态实时显示
- **自动刷新** - 数据每3秒自动更新

### 🎮 控制功能
- **终端复位** - 重启电池管理系统
- **充电控制** - 允许/禁止充电功能
- **放电控制** - 允许/禁止放电功能
- **蜂鸣控制** - 开启/关闭蜂鸣器

## 📁 项目结构

```
web_project/                    # 项目根目录
├── app.py                     # Flask主应用文件
├── start.py                   # 启动脚本
├── requirements.txt           # Python依赖列表
├── README.md                  # 项目说明文档
├── web/                       # Web相关文件夹
│   ├── templates/             # HTML模板文件夹
│   │   └── index.html        # 主页面模板
│   └── static/               # 静态资源文件夹（CSS/JS/图片）
└── other/                     # 其他文件夹
    ├── test_app.py           # 应用测试脚本
    ├── simple_test.py        # 简单测试脚本
    └── 项目说明.md           # 项目详细说明
```

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
```bash
cd web_project
python start.py
```

### 方法二：直接启动Flask
```bash
cd web_project
pip install -r requirements.txt
python app.py
```

## 📱 访问方式

启动成功后，可通过以下方式访问：

- **本机访问**: http://localhost:5000
- **手机访问**: http://你的电脑IP:5000

## 🔧 技术架构

### 后端技术
- **Flask** - Python Web框架
- **Threading** - 多线程数据更新
- **JSON API** - RESTful接口设计

### 前端技术
- **HTML5** - 语义化标记
- **CSS3** - 现代化样式设计
- **JavaScript** - 动态交互功能
- **Fetch API** - 异步数据请求

### 设计特色
- **响应式设计** - 适配各种屏幕尺寸
- **渐变背景** - 美观的紫色渐变背景
- **卡片布局** - 现代化的卡片式组件
- **浮动按钮** - 便捷的浮动控制按钮
- **状态指示** - 实时连接状态显示

## 📊 数据展示

### 主要参数卡片
- 剩余电量 (SOC) - 百分比显示
- 总电压 - 伏特单位
- 总电流 - 安培单位  
- 温度 - 摄氏度单位
- 剩余容量 - 安时单位

### 电芯电压网格
- 20节电芯电压实时显示
- 最高电压标记（黄色）
- 最低电压标记（蓝色）
- 5列网格布局

### 系统状态列表
- 静置状态
- 充电状态
- MOS管状态
- 保护状态

## 🎮 控制界面

### 头部控制按钮
- **刷新数据** - 手动刷新数据
- **自动刷新** - 开启/关闭自动刷新

### 浮动控制按钮
- 🔄 **终端复位** - 蓝色按钮
- 🔌 **允许充电** - 绿色按钮
- 🔊 **开启蜂鸣** - 黄色按钮
- ⚡ **禁止放电** - 红色按钮

## 📡 API接口

### 获取数据接口
```
GET /api/data
Response: JSON格式的电池数据
```

### 控制命令接口
```
POST /api/command
Content-Type: application/json
Body: {"command": "命令名称"}
Response: {"success": true, "message": "执行结果"}
```

### 支持的命令
- `refresh_data` - 刷新数据
- `auto_refresh` - 自动刷新
- `reset_terminal` - 终端复位
- `buzzer_on` - 开启蜂鸣器
- `buzzer_off` - 关闭蜂鸣器
- `allow_charge` - 允许充电
- `forbid_charge` - 禁止充电
- `allow_discharge` - 允许放电
- `forbid_discharge` - 禁止放电

## 🔄 自动更新机制

- **更新频率** - 每3秒自动刷新一次
- **状态指示** - 右上角显示连接状态
- **手动控制** - 可开启/关闭自动刷新
- **错误处理** - 网络错误时显示提示

## 🎨 界面元素

### 颜色方案
- **主色调** - 紫色渐变 (#667eea → #764ba2)
- **成功色** - 绿色 (#34a853)
- **警告色** - 黄色 (#fbbc04)
- **危险色** - 红色 (#ea4335)
- **信息色** - 蓝色 (#4285f4)

### 组件样式
- **圆角设计** - 统一的圆角风格
- **阴影效果** - 柔和的投影效果
- **悬停动画** - 按钮悬停动画
- **过渡效果** - 流畅的过渡动画

## 📱 移动端优化

- **触控友好** - 按钮大小适合触控
- **滚动优化** - 流畅的滚动体验
- **字体大小** - 适合手机阅读的字体
- **布局适配** - 完美适配手机屏幕

## 🔒 安全特性

- **命令验证** - 控制命令安全验证
- **错误处理** - 完善的错误处理机制
- **状态监控** - 实时连接状态监控

## 🎯 使用场景

- **实时监控** - 电池状态实时监控
- **远程控制** - 手机远程控制电池
- **数据分析** - 电池数据分析查看
- **故障诊断** - 电池故障快速诊断

---

**万洋锂电池监控系统** - 让电池监控更简单、更直观！ 🔋✨
