#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新抓包数据分析器
分析万洋锂电池抓包数据.json文件
"""

import xml.etree.ElementTree as ET
import base64
import json
import urllib.parse
from datetime import datetime

def analyze_new_data():
    """分析新的抓包数据"""
    print("🔍 分析新的抓包数据...")
    
    try:
        # 解析XML文件
        tree = ET.parse('万洋锂电池抓包数据.json')
        root = tree.getroot()
        
        print(f"📊 找到 {len(root.findall('item'))} 个请求记录")
        
        # 分析每个请求
        for i, item in enumerate(root.findall('item')):
            url = item.find('url').text
            method = item.find('method').text
            response_elem = item.find('response')
            
            print(f"\n🔗 请求 {i+1}: {method} {url}")
            
            if response_elem is not None and response_elem.get('base64') == 'true':
                # 解码响应数据
                response_data = base64.b64decode(response_elem.text).decode('utf-8')
                
                # 提取JSON部分
                json_start = response_data.find('{')
                if json_start != -1:
                    json_data = response_data[json_start:]
                    try:
                        data = json.loads(json_data)
                        
                        # 分析不同类型的API
                        if '/userBattery/api/list' in url:
                            print("   📋 电池列表数据")
                            if 'result' in data and data['result']:
                                for battery in data['result']:
                                    user_battery = battery['userBattery']
                                    print(f"      🔋 电池: {user_battery['batteryCode']}")
                                    print(f"      📱 客户端: {user_battery['clientId']}")
                                    print(f"      🏷️ 类型: {user_battery['batteryType']}")
                        
                        elif '/fnjbattery/realTime' in url:
                            print("   ⚡ 实时数据")
                            if 'detail' in data:
                                detail_str = data['detail']
                                detail_data = json.loads(detail_str)
                                
                                print(f"      📍 位置: {detail_data.get('latitude', 'N/A')}, {detail_data.get('longitude', 'N/A')}")
                                print(f"      📱 设备: {detail_data.get('mobile', 'N/A')}")
                                
                                # 解析电池包信息
                                if 'batteryPackageInfo' in detail_data:
                                    battery_info = json.loads(detail_data['batteryPackageInfo'])
                                    print(f"      🔋 SOC: {battery_info.get('soc', 'N/A')}%")
                                    print(f"      ⚡ 电压: {battery_info.get('totalVoltage', 'N/A')}V")
                                    print(f"      🔌 电流: {battery_info.get('totalCurrent', 'N/A')}A")
                                    print(f"      🌡️ 温度: {battery_info.get('BMSTemp', 'N/A')}°C")
                                    print(f"      🔄 循环: {battery_info.get('loopTimes', 'N/A')}次")
                        
                        elif '/parseProtectionAndWarning' in url:
                            print("   ⚠️ 保护和警告")
                            if 'result' in data:
                                result = data['result']
                                alerts = result.get('alerts', [])
                                protections = result.get('protections', [])
                                print(f"      📢 告警: {len(alerts)}条")
                                print(f"      🛡️ 保护: {len(protections)}条")
                                
                                for alert in alerts[:3]:  # 只显示前3条
                                    print(f"         - {alert}")
                        
                        elif '/deviceParameters' in url:
                            print("   ⚙️ 设备参数")
                            if 'detail' in data:
                                detail_str = data['detail']
                                detail_data = json.loads(detail_str)
                                print(f"      🔌 充电过流保护: {detail_data.get('chargingOverCurrentProtectionValue', 'N/A')}A")
                                print(f"      🔋 放电过流保护: {detail_data.get('dischargeOverCurrent1ProtectionValue', 'N/A')}A")
                                print(f"      ⚡ 短路保护: {detail_data.get('shortCircuitProtectionCurrent', 'N/A')}A")
                        
                    except json.JSONDecodeError as e:
                        print(f"      ❌ JSON解析失败: {e}")
            
            print("-" * 60)
        
        print("\n✅ 数据分析完成")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def extract_control_apis():
    """提取控制相关的API"""
    print("\n🎮 查找控制相关的API...")
    
    # 从小程序界面截图可以看到的控制功能：
    control_functions = [
        "终端复位",      # 重启设备
        "开启蜂鸣",      # 开启蜂鸣器
        "关闭蜂鸣",      # 关闭蜂鸣器
        "允许放电",      # 允许放电
        "禁止放电",      # 禁止放电
        "允许充电",      # 允许充电
        "禁止充电",      # 禁止充电
        "控制放电流",    # 控制放电流 (60A, 80A, 100A)
        "切换电池"       # 切换电池
    ]
    
    print("📋 小程序中的控制功能:")
    for i, func in enumerate(control_functions, 1):
        print(f"   {i}. {func}")
    
    print("\n💡 需要通过抓包获取控制API的具体实现")

def analyze_ui_structure():
    """分析小程序UI结构"""
    print("\n🎨 小程序UI结构分析:")
    
    ui_elements = {
        "主界面": {
            "SOC圆形进度条": "99%显示，带渐变色",
            "预计充满时间": "0小时4分钟",
            "充电电流": "10A",
            "放电状态": "关",
            "均衡保护": "开",
            "电池编号": "BT2072055010032504140408",
            "更新时间": "2025年06月18日11时02分"
        },
        "详情页面": {
            "参数区域": {
                "GPS预估总里程": "15.2km",
                "速度": "0km/h", 
                "循环次数": "1次",
                "平均电压": "4.209v",
                "最高电压": "4.212v",
                "最低电压": "4.207v"
            },
            "温度区域": {
                "MOS温度": "34°c",
                "环境温度": "35°c", 
                "电芯温度": "33°c/32°c"
            },
            "状态区域": {
                "电池状态": "充电",
                "告警状态": "正常",
                "GSM信号": "优 (23)"
            }
        },
        "电芯页面": {
            "电芯电压网格": "20个电芯，4.208v-4.212v",
            "电池定位": "地图显示位置"
        },
        "控制页面": {
            "保护信息": "暂无",
            "告警信息": "4条状态信息",
            "控制按钮": "控制、详情、解绑、切换电池"
        }
    }
    
    for section, details in ui_elements.items():
        print(f"\n📱 {section}:")
        if isinstance(details, dict):
            for key, value in details.items():
                if isinstance(value, dict):
                    print(f"   📋 {key}:")
                    for k, v in value.items():
                        print(f"      • {k}: {v}")
                else:
                    print(f"   • {key}: {value}")
        else:
            print(f"   • {details}")

if __name__ == "__main__":
    analyze_new_data()
    extract_control_apis()
    analyze_ui_structure()
