#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
锂电池数据分析器
从Burp Suite抓包数据中提取和分析锂电池信息
"""

import xml.etree.ElementTree as ET
import base64
import json
import urllib.parse
from datetime import datetime

class BatteryDataAnalyzer:
    def __init__(self, xml_file_path):
        self.xml_file_path = xml_file_path
        self.battery_data = []
        self.real_time_data = []
        self.device_parameters = []
        self.protection_warnings = []
        
    def parse_xml_data(self):
        """解析XML抓包数据"""
        try:
            tree = ET.parse(self.xml_file_path)
            root = tree.getroot()
            
            for item in root.findall('item'):
                url = item.find('url').text
                response_elem = item.find('response')
                
                if response_elem is not None and response_elem.get('base64') == 'true':
                    # 解码base64响应数据
                    response_data = base64.b64decode(response_elem.text).decode('utf-8')
                    
                    # 提取JSON部分（跳过HTTP头）
                    json_start = response_data.find('{')
                    if json_start != -1:
                        json_data = response_data[json_start:]
                        try:
                            data = json.loads(json_data)
                            self._categorize_data(url, data)
                        except json.JSONDecodeError:
                            print(f"JSON解析失败: {url}")
                            
        except Exception as e:
            print(f"解析XML文件时出错: {e}")
    
    def _categorize_data(self, url, data):
        """根据URL类型分类数据"""
        timestamp = datetime.now().isoformat()
        
        if '/userBattery/api/list' in url:
            # 用户电池列表数据
            if 'result' in data and data['result']:
                for battery in data['result']:
                    battery_info = {
                        'timestamp': timestamp,
                        'url': url,
                        'data': battery
                    }
                    self.battery_data.append(battery_info)
                    
        elif '/fnjbattery/realTime' in url:
            # 实时电池数据
            if 'detail' in data:
                detail_str = data['detail']
                try:
                    detail_data = json.loads(detail_str)
                    real_time_info = {
                        'timestamp': timestamp,
                        'url': url,
                        'data': detail_data
                    }
                    self.real_time_data.append(real_time_info)
                except json.JSONDecodeError:
                    print("实时数据JSON解析失败")
                    
        elif '/deviceParameters' in url:
            # 设备参数数据
            if 'detail' in data:
                detail_str = data['detail']
                try:
                    detail_data = json.loads(detail_str)
                    param_info = {
                        'timestamp': timestamp,
                        'url': url,
                        'data': detail_data
                    }
                    self.device_parameters.append(param_info)
                except json.JSONDecodeError:
                    print("设备参数JSON解析失败")
                    
        elif '/parseProtectionAndWarning' in url:
            # 保护和警告数据
            if 'result' in data:
                warning_info = {
                    'timestamp': timestamp,
                    'url': url,
                    'data': data['result']
                }
                self.protection_warnings.append(warning_info)
    
    def analyze_battery_basic_info(self):
        """分析电池基本信息"""
        print("=== 电池基本信息分析 ===")
        
        if not self.battery_data:
            print("未找到电池基本信息数据")
            return
            
        for battery_info in self.battery_data:
            battery = battery_info['data']
            if 'userBattery' in battery:
                user_battery = battery['userBattery']
                print(f"电池ID: {user_battery.get('batteryCode', 'N/A')}")
                print(f"电池类型: {user_battery.get('batteryType', 'N/A')}")
                print(f"客户端ID: {user_battery.get('clientId', 'N/A')}")
                print(f"创建时间: {user_battery.get('createTime', 'N/A')}")
                print(f"创建者: {user_battery.get('createBy', 'N/A')}")
                print("-" * 50)
    
    def analyze_real_time_data(self):
        """分析实时数据"""
        print("\n=== 实时数据分析 ===")
        
        if not self.real_time_data:
            print("未找到实时数据")
            return
            
        for rt_info in self.real_time_data:
            data = rt_info['data']
            
            print(f"设备号: {data.get('mobile', 'N/A')}")
            print(f"心跳时间: {data.get('heartBeatTime', 'N/A')}")
            print(f"位置信息: 纬度{data.get('latitude', 'N/A')}, 经度{data.get('longitude', 'N/A')}")
            print(f"海拔: {data.get('altitude', 'N/A')}m")
            print(f"速度: {data.get('speed', 'N/A')}km/h")
            print(f"方向: {data.get('direction', 'N/A')}°")
            
            # 解析电池包信息
            if 'batteryPackageInfo' in data:
                try:
                    battery_info = json.loads(data['batteryPackageInfo'])
                    print(f"\n电池包信息:")
                    print(f"  总电压: {battery_info.get('totalVoltage', 'N/A')}V")
                    print(f"  总电流: {battery_info.get('totalCurrent', 'N/A')}A")
                    print(f"  SOC: {battery_info.get('soc', 'N/A')}%")
                    print(f"  电芯数量: {battery_info.get('cellQuantity', 'N/A')}")
                    print(f"  温度数量: {battery_info.get('tempQuantity', 'N/A')}")
                    print(f"  BMS温度: {battery_info.get('BMSTemp', 'N/A')}°C")
                    print(f"  剩余容量: {battery_info.get('residualCapacity', 'N/A')}Ah")
                    print(f"  当前容量: {battery_info.get('currentCapacity', 'N/A')}Ah")
                    print(f"  循环次数: {battery_info.get('loopTimes', 'N/A')}")
                    
                    # 电芯电压详情
                    if 'cellVoltageDetail' in battery_info:
                        voltages = battery_info['cellVoltageDetail']
                        print(f"  电芯电压范围: {min(map(float, voltages)):.3f}V - {max(map(float, voltages)):.3f}V")
                        print(f"  电芯电压差: {max(map(float, voltages)) - min(map(float, voltages)):.3f}V")
                    
                    # 温度详情
                    if 'tempDetailInfo' in battery_info:
                        temps = battery_info['tempDetailInfo']
                        print(f"  温度范围: {min(map(float, temps)):.1f}°C - {max(map(float, temps)):.1f}°C")
                        
                except json.JSONDecodeError:
                    print("电池包信息解析失败")
            
            print("-" * 50)
    
    def analyze_protection_warnings(self):
        """分析保护和警告信息"""
        print("\n=== 保护和警告信息分析 ===")
        
        if not self.protection_warnings:
            print("未找到保护和警告数据")
            return
            
        for warning_info in self.protection_warnings:
            data = warning_info['data']
            
            if 'alerts' in data and data['alerts']:
                print("当前警告:")
                for alert in data['alerts']:
                    print(f"  - {alert}")
            else:
                print("当前无警告")
                
            if 'protections' in data and data['protections']:
                print("当前保护:")
                for protection in data['protections']:
                    print(f"  - {protection}")
            else:
                print("当前无保护触发")
                
            print("-" * 50)
    
    def analyze_device_parameters(self):
        """分析设备参数"""
        print("\n=== 设备参数分析 ===")
        
        if not self.device_parameters:
            print("未找到设备参数数据")
            return
            
        for param_info in self.device_parameters:
            data = param_info['data']
            
            print("充电参数:")
            print(f"  充电电流校准K值: {data.get('chargeCurrentCalibrationKValue', 'N/A')}")
            print(f"  充电过流保护值: {data.get('chargingOverCurrentProtectionValue', 'N/A')}A")
            print(f"  充电过流保护延时: {data.get('chargeOverCurrentProtectionDelay', 'N/A')}s")
            print(f"  充电过流告警值: {data.get('chargingOverCurrentAlarmValue', 'N/A')}A")
            
            print("\n放电参数:")
            print(f"  放电电流校准K值: {data.get('dischargeCurrentCalibrationKValue', 'N/A')}")
            print(f"  放电过流1级保护值: {data.get('dischargeOverCurrent1ProtectionValue', 'N/A')}A")
            print(f"  放电过流1级保护延时: {data.get('dischargeOverCurrent1ProtectionDelay', 'N/A')}s")
            print(f"  放电过流2级保护值: {data.get('dischargeOverCurrent2ProtectionValue', 'N/A')}A")
            print(f"  放电过流2级保护延时: {data.get('dischargeOverCurrent2ProtectionDelay', 'N/A')}s")
            print(f"  放电过流告警值: {data.get('dischargeOverCurrentAlarmValue', 'N/A')}A")
            
            print("\n短路保护:")
            print(f"  短路保护电流: {data.get('shortCircuitProtectionCurrent', 'N/A')}A")
            print(f"  短路保护延时: {data.get('shortCircuitProtectionDelay', 'N/A')}ms")
            
            print("-" * 50)
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print("\n" + "="*60)
        print("锂电池数据分析汇总报告")
        print("="*60)
        
        print(f"数据采集时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"电池基本信息记录数: {len(self.battery_data)}")
        print(f"实时数据记录数: {len(self.real_time_data)}")
        print(f"设备参数记录数: {len(self.device_parameters)}")
        print(f"保护警告记录数: {len(self.protection_warnings)}")
        
        # 分析各类数据
        self.analyze_battery_basic_info()
        self.analyze_real_time_data()
        self.analyze_protection_warnings()
        self.analyze_device_parameters()

def main():
    """主函数"""
    analyzer = BatteryDataAnalyzer('抓包数据.txt')
    analyzer.parse_xml_data()
    analyzer.generate_summary_report()

if __name__ == "__main__":
    main()
