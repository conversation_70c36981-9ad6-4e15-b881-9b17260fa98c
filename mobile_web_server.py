#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 手机端Web服务器
Mobile Web Server for Wanyang Battery Monitoring
"""

from flask import Flask, render_template_string, jsonify
import json
import os
from datetime import datetime
import threading
import time

app = Flask(__name__)

# 基于真实抓包数据的电池数据
battery_data = {
    "soc": 99,
    "total_voltage": 84.10,
    "total_current": -10.00,
    "total_power": -841.00,
    "mos_temp": 34,
    "env_temp": 35,
    "cell_temp_range": "33°C/32°C",
    "total_mileage": 15.2,
    "gsm_signal": "优(23)",
    "battery_code": "BT2072055010032504140408",
    "update_time": "2025年06月18日11时02分",
    "cell_voltages": [
        4.208, 4.209, 4.210, 4.209, 4.207,
        4.209, 4.212, 4.210, 4.209, 4.210,
        4.210, 4.210, 4.209, 4.208, 4.208,
        4.209, 4.210, 4.210, 4.208, 4.207
    ],
    "charge_status": False,
    "discharge_status": True,
    "protection_status": [],
    "alarm_status": []
}

# 手机端HTML模板
MOBILE_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万洋智行 - 电池监控</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh; color: #333;
        }
        .container { max-width: 400px; margin: 0 auto; background: #f5f7fa; min-height: 100vh; }
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white; padding: 20px; text-align: center; border-radius: 0 0 20px 20px;
        }
        .header h1 { font-size: 24px; font-weight: 600; margin-bottom: 5px; }
        .main-display {
            background: white; margin: 20px; border-radius: 20px; padding: 30px 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); text-align: center;
        }
        .soc-circle {
            width: 140px; height: 140px; border-radius: 50%;
            background: conic-gradient(from -90deg, #4CAF50 0%, #4CAF50 var(--percentage), #e8f5e9 var(--percentage), #e8f5e9 100%);
            display: flex; align-items: center; justify-content: center;
            margin: 0 auto 20px; position: relative; box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
        }
        .soc-circle::before {
            content: ''; width: 110px; height: 110px; background: white; border-radius: 50%;
            position: absolute; box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .soc-text { font-size: 32px; font-weight: bold; color: #2e7d32; z-index: 1; }
        .battery-info {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white; margin: 20px; border-radius: 15px; padding: 20px;
            box-shadow: 0 8px 32px rgba(30, 60, 114, 0.3);
        }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 15px; }
        .info-item { text-align: center; }
        .info-label { font-size: 11px; opacity: 0.8; margin-bottom: 3px; }
        .info-value { font-size: 14px; font-weight: 600; }
        .control-section {
            background: white; border-radius: 15px; padding: 20px; margin: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .control-buttons { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
        .control-btn {
            padding: 15px; border: none; border-radius: 12px; font-size: 14px; font-weight: 600;
            cursor: pointer; transition: all 0.3s; background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white; box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
        }
        .control-btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(30, 60, 114, 0.4); }
        .cell-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 12px; margin-top: 20px; }
        .cell-item {
            background: white; padding: 12px 8px; border-radius: 10px; text-align: center;
            border: 2px solid #e9ecef; transition: all 0.3s; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .cell-item.high { border-color: #ff9800; background: #fff8e1; }
        .cell-item.low { border-color: #2196f3; background: #e3f2fd; }
        .cell-number { font-size: 10px; color: #6c757d; margin-bottom: 4px; }
        .cell-voltage { font-size: 13px; font-weight: bold; color: #495057; }
        .refresh-btn {
            position: fixed; bottom: 30px; right: 30px; width: 60px; height: 60px; border-radius: 50%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); border: none;
            color: white; font-size: 24px; cursor: pointer; box-shadow: 0 8px 30px rgba(30, 60, 114, 0.4);
            transition: all 0.3s;
        }
        .refresh-btn:hover { transform: scale(1.1); }
        .status-indicator {
            position: fixed; top: 20px; right: 20px; padding: 8px 12px;
            background: rgba(76, 175, 80, 0.9); color: white; border-radius: 20px;
            font-size: 12px; font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-indicator" id="statusIndicator">已连接</div>
        
        <div class="header">
            <h1>万洋智行</h1>
        </div>

        <div class="main-display">
            <div class="soc-circle" id="socCircle">
                <div class="soc-text" id="socText">--</div>
            </div>
            <div style="font-size: 12px; color: #666; margin-top: 10px;" id="updateTime">等待数据...</div>
        </div>

        <div class="battery-info">
            <div style="font-size: 14px; font-weight: 600; margin-bottom: 5px;" id="batteryCode">电池编号: --</div>
            
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">总电压</div>
                    <div class="info-value" id="totalVoltage">--V</div>
                </div>
                <div class="info-item">
                    <div class="info-label">电流</div>
                    <div class="info-value" id="totalCurrent">--A</div>
                </div>
                <div class="info-item">
                    <div class="info-label">功率</div>
                    <div class="info-value" id="totalPower">--W</div>
                </div>
                <div class="info-item">
                    <div class="info-label">MOS温度</div>
                    <div class="info-value" id="mosTemp">--°C</div>
                </div>
                <div class="info-item">
                    <div class="info-label">环境温度</div>
                    <div class="info-value" id="envTemp">--°C</div>
                </div>
                <div class="info-item">
                    <div class="info-label">电芯温度</div>
                    <div class="info-value" id="cellTemp">--°C</div>
                </div>
                <div class="info-item">
                    <div class="info-label">总里程</div>
                    <div class="info-value" id="totalMileage">--km</div>
                </div>
                <div class="info-item">
                    <div class="info-label">GSM信号</div>
                    <div class="info-value" id="gsmSignal">--</div>
                </div>
            </div>
        </div>

        <div class="control-section">
            <h3 style="margin-bottom: 15px; color: #1e3c72;">控制面板</h3>
            <div class="control-buttons">
                <button class="control-btn" onclick="sendCommand('reset')">终端复位</button>
                <button class="control-btn" onclick="sendCommand('buzzer_on')">开启蜂鸣</button>
                <button class="control-btn" onclick="sendCommand('buzzer_off')">关闭蜂鸣</button>
                <button class="control-btn" onclick="sendCommand('allow_discharge')">允许放电</button>
                <button class="control-btn" onclick="sendCommand('forbid_discharge')">禁止放电</button>
                <button class="control-btn" onclick="sendCommand('allow_charge')">允许充电</button>
            </div>
        </div>

        <div class="control-section">
            <h3 style="margin-bottom: 15px; color: #1e3c72;">电芯电压 (<span id="cellCount">20</span>节)</h3>
            <div class="cell-grid" id="cellGrid"></div>
        </div>

        <button class="refresh-btn" onclick="refreshData()" title="刷新数据">🔄</button>
    </div>

    <script>
        function updateDisplay() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    // 更新SOC
                    const socCircle = document.getElementById('socCircle');
                    const socText = document.getElementById('socText');
                    const percentage = data.soc * 3.6;
                    socCircle.style.setProperty('--percentage', percentage + 'deg');
                    socText.textContent = data.soc + '%';

                    // 更新基本信息
                    document.getElementById('batteryCode').textContent = '电池编号: ' + data.battery_code;
                    document.getElementById('updateTime').textContent = '更新时间: ' + data.update_time;
                    
                    // 更新数值
                    document.getElementById('totalVoltage').textContent = data.total_voltage + 'V';
                    document.getElementById('totalCurrent').textContent = data.total_current + 'A';
                    document.getElementById('totalPower').textContent = data.total_power + 'W';
                    document.getElementById('mosTemp').textContent = data.mos_temp + '°C';
                    document.getElementById('envTemp').textContent = data.env_temp + '°C';
                    document.getElementById('cellTemp').textContent = data.cell_temp_range;
                    document.getElementById('totalMileage').textContent = data.total_mileage + 'km';
                    document.getElementById('gsmSignal').textContent = data.gsm_signal;
                    
                    // 更新电芯网格
                    updateCellGrid(data.cell_voltages);
                })
                .catch(error => console.error('获取数据失败:', error));
        }

        function updateCellGrid(cellVoltages) {
            const cellGrid = document.getElementById('cellGrid');
            cellGrid.innerHTML = '';
            
            const maxVoltage = Math.max(...cellVoltages);
            const minVoltage = Math.min(...cellVoltages);
            
            cellVoltages.forEach((voltage, index) => {
                const cellItem = document.createElement('div');
                cellItem.className = 'cell-item';
                
                if (voltage >= maxVoltage - 0.001) {
                    cellItem.classList.add('high');
                } else if (voltage <= minVoltage + 0.001) {
                    cellItem.classList.add('low');
                }
                
                cellItem.innerHTML = `
                    <div class="cell-number">${index + 1}</div>
                    <div class="cell-voltage">${voltage.toFixed(3)}V</div>
                `;
                
                cellGrid.appendChild(cellItem);
            });
        }

        function sendCommand(command) {
            const button = event.target;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.textContent = '执行中...';
            button.style.background = '#adb5bd';
            
            fetch('/api/command', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ command: command })
            })
            .then(response => response.json())
            .then(data => {
                button.disabled = false;
                button.textContent = originalText;
                button.style.background = '';
                showMessage(data.message || '命令执行成功');
            })
            .catch(error => {
                button.disabled = false;
                button.textContent = originalText;
                button.style.background = '';
                showMessage('命令执行失败');
            });
        }

        function refreshData() {
            document.getElementById('statusIndicator').textContent = '刷新中...';
            document.getElementById('statusIndicator').style.background = 'rgba(255, 152, 0, 0.9)';
            
            updateDisplay();
            
            setTimeout(() => {
                document.getElementById('statusIndicator').textContent = '已连接';
                document.getElementById('statusIndicator').style.background = 'rgba(76, 175, 80, 0.9)';
            }, 1000);
        }

        function showMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed; top: 70px; left: 50%; transform: translateX(-50%);
                background: rgba(76, 175, 80, 0.9); color: white; padding: 10px 20px;
                border-radius: 20px; font-size: 14px; z-index: 9999;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            `;
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);
            setTimeout(() => {
                if (document.body.contains(messageDiv)) {
                    document.body.removeChild(messageDiv);
                }
            }, 2000);
        }

        // 初始化和自动刷新
        document.addEventListener('DOMContentLoaded', updateDisplay);
        setInterval(updateDisplay, 5000); // 5秒自动刷新
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页面 - 手机端界面"""
    return render_template_string(MOBILE_TEMPLATE)

@app.route('/api/data')
def get_data():
    """获取电池数据API"""
    return jsonify(battery_data)

@app.route('/api/command', methods=['POST'])
def send_command():
    """发送控制命令API"""
    try:
        from flask import request
        data = request.get_json()
        command = data.get('command')
        
        # 模拟命令执行
        print(f"🎮 执行控制命令: {command}")
        
        command_names = {
            'reset': '终端复位',
            'buzzer_on': '开启蜂鸣器',
            'buzzer_off': '关闭蜂鸣器',
            'allow_discharge': '允许放电',
            'forbid_discharge': '禁止放电',
            'allow_charge': '允许充电'
        }
        
        return jsonify({
            'success': True,
            'message': f'{command_names.get(command, command)} 执行成功'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'命令执行失败: {str(e)}'
        }), 500

def update_battery_data():
    """后台更新电池数据"""
    while True:
        time.sleep(5)  # 每5秒更新一次
        
        # 模拟数据变化
        import random
        battery_data['soc'] = min(100, max(0, battery_data['soc'] + random.uniform(-0.1, 0.1)))
        battery_data['total_voltage'] += random.uniform(-0.05, 0.05)
        battery_data['total_current'] += random.uniform(-0.5, 0.5)
        battery_data['total_power'] = battery_data['total_voltage'] * battery_data['total_current']
        battery_data['mos_temp'] += random.randint(-1, 1)
        battery_data['update_time'] = datetime.now().strftime('%Y年%m月%d日%H时%M分')
        
        # 更新电芯电压
        for i in range(len(battery_data['cell_voltages'])):
            battery_data['cell_voltages'][i] += random.uniform(-0.002, 0.002)
            battery_data['cell_voltages'][i] = round(battery_data['cell_voltages'][i], 3)

def main():
    """启动Web服务器"""
    print("🔋 万洋锂电池监控系统 - 手机端Web服务器")
    print("=" * 50)
    print("🌐 启动Web服务器...")
    print("📱 手机端访问地址:")
    print("   • 本机访问: http://localhost:5000")
    print("   • 局域网访问: http://你的IP地址:5000")
    print("🎮 支持的控制命令:")
    print("   • 终端复位、蜂鸣器控制")
    print("   • 充放电控制")
    print("🔄 数据每5秒自动更新")
    print("=" * 50)
    
    # 启动后台数据更新线程
    update_thread = threading.Thread(target=update_battery_data, daemon=True)
    update_thread.start()
    
    # 启动Web服务器
    app.run(host='0.0.0.0', port=5000, debug=False)

if __name__ == '__main__':
    main()
