#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电池数据获取模块
自动调用API接口获取电池数据，包含错误处理和重试机制
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, Optional, Any
import urllib.parse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('battery_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class BatteryDataFetcher:
    def __init__(self, config_file='api_config.json'):
        """初始化数据获取器"""
        self.config = self._load_config(config_file)
        self.session = requests.Session()
        self._setup_session()
        self.logger = logging.getLogger(__name__)
        
    def _load_config(self, config_file: str) -> Dict:
        """加载API配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"配置文件 {config_file} 不存在")
            raise
        except json.JSONDecodeError:
            self.logger.error(f"配置文件 {config_file} 格式错误")
            raise
    
    def _setup_session(self):
        """设置请求会话"""
        # 设置通用请求头
        headers = self.config['common_headers'].copy()
        if 'auth' in self.config and 'access_token' in self.config['auth']:
            headers['X-Access-Token'] = self.config['auth']['access_token']
        if 'auth' in self.config and 'user_agent' in self.config['auth']:
            headers['User-Agent'] = self.config['auth']['user_agent']
        
        self.session.headers.update(headers)
        
        # 设置超时和重试
        self.session.timeout = 10
        
    def _make_request(self, url: str, params: Dict = None, max_retries: int = 3) -> Optional[Dict]:
        """发送HTTP请求，包含重试机制"""
        for attempt in range(max_retries):
            try:
                self.logger.info(f"请求 {url} (尝试 {attempt + 1}/{max_retries})")
                
                response = self.session.get(url, params=params)
                response.raise_for_status()
                
                # 解析JSON响应
                data = response.json()
                
                # 检查业务状态码
                if data.get('code') == 200 or data.get('success') is True:
                    self.logger.info(f"请求成功: {url}")
                    return data
                else:
                    self.logger.warning(f"业务错误: {data.get('message', '未知错误')}")
                    return None
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{max_retries})")
            except requests.exceptions.ConnectionError:
                self.logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries})")
            except requests.exceptions.HTTPError as e:
                self.logger.error(f"HTTP错误: {e}")
                return None
            except json.JSONDecodeError:
                self.logger.error("响应不是有效的JSON格式")
                return None
            except Exception as e:
                self.logger.error(f"未知错误: {e}")
                return None
            
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
        
        self.logger.error(f"请求失败，已达到最大重试次数: {url}")
        return None
    
    def get_battery_list(self) -> Optional[Dict]:
        """获取电池列表"""
        if 'battery_list' not in self.config['endpoints']:
            self.logger.error("未找到电池列表API配置")
            return None
        
        endpoint = self.config['endpoints']['battery_list']
        url = self.config['base_url'] + endpoint['path']
        
        # 解析查询参数
        params = {}
        if endpoint['query']:
            params = dict(urllib.parse.parse_qsl(endpoint['query']))
        
        return self._make_request(url, params)
    
    def get_real_time_data(self) -> Optional[Dict]:
        """获取实时电池数据"""
        if 'real_time_data' not in self.config['endpoints']:
            self.logger.error("未找到实时数据API配置")
            return None
        
        endpoint = self.config['endpoints']['real_time_data']
        url = self.config['base_url'] + endpoint['path']
        
        # 解析查询参数
        params = {}
        if endpoint['query']:
            params = dict(urllib.parse.parse_qsl(endpoint['query']))
        
        return self._make_request(url, params)
    
    def get_device_parameters(self) -> Optional[Dict]:
        """获取设备参数"""
        if 'device_parameters' not in self.config['endpoints']:
            self.logger.error("未找到设备参数API配置")
            return None
        
        endpoint = self.config['endpoints']['device_parameters']
        url = self.config['base_url'] + endpoint['path']
        
        # 解析查询参数
        params = {}
        if endpoint['query']:
            params = dict(urllib.parse.parse_qsl(endpoint['query']))
        
        return self._make_request(url, params)
    
    def get_protection_warnings(self, bms_status_info: Dict) -> Optional[Dict]:
        """获取保护和警告信息"""
        if 'protection_warnings' not in self.config['endpoints']:
            self.logger.error("未找到保护警告API配置")
            return None
        
        endpoint = self.config['endpoints']['protection_warnings']
        url = self.config['base_url'] + endpoint['path']
        
        # 构建查询参数
        params = {
            'bmsStatusInfo': json.dumps(bms_status_info, separators=(',', ':'))
        }
        
        return self._make_request(url, params)
    
    def get_all_data(self) -> Dict[str, Any]:
        """获取所有电池数据"""
        result = {
            'timestamp': datetime.now().isoformat(),
            'battery_list': None,
            'real_time_data': None,
            'device_parameters': None,
            'protection_warnings': None,
            'success': False
        }
        
        # 获取电池列表
        battery_list = self.get_battery_list()
        if battery_list:
            result['battery_list'] = battery_list
        
        # 获取实时数据
        real_time_data = self.get_real_time_data()
        if real_time_data:
            result['real_time_data'] = real_time_data
            
            # 如果有实时数据，尝试获取保护警告信息
            if 'detail' in real_time_data:
                try:
                    detail_data = json.loads(real_time_data['detail'])
                    if 'bmsStatusInfo' in detail_data:
                        bms_status = json.loads(detail_data['bmsStatusInfo'])
                        protection_warnings = self.get_protection_warnings(bms_status)
                        if protection_warnings:
                            result['protection_warnings'] = protection_warnings
                except (json.JSONDecodeError, KeyError) as e:
                    self.logger.warning(f"解析BMS状态信息失败: {e}")
        
        # 获取设备参数
        device_parameters = self.get_device_parameters()
        if device_parameters:
            result['device_parameters'] = device_parameters
        
        # 判断是否成功获取到关键数据
        result['success'] = bool(result['real_time_data'])
        
        return result
    
    def test_connection(self) -> bool:
        """测试API连接"""
        self.logger.info("测试API连接...")
        
        # 尝试获取电池列表来测试连接
        battery_list = self.get_battery_list()
        if battery_list:
            self.logger.info("✅ API连接测试成功")
            return True
        else:
            self.logger.error("❌ API连接测试失败")
            return False

def main():
    """主函数 - 测试数据获取功能"""
    try:
        fetcher = BatteryDataFetcher()
        
        # 测试连接
        if not fetcher.test_connection():
            print("API连接失败，请检查配置")
            return
        
        # 获取所有数据
        print("正在获取电池数据...")
        data = fetcher.get_all_data()
        
        if data['success']:
            print("✅ 数据获取成功")
            
            # 保存数据到文件
            filename = f"battery_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"📁 数据已保存到: {filename}")
            
            # 显示关键信息
            if data['real_time_data'] and 'detail' in data['real_time_data']:
                detail = json.loads(data['real_time_data']['detail'])
                if 'batteryPackageInfo' in detail:
                    battery_info = json.loads(detail['batteryPackageInfo'])
                    print(f"🔋 SOC: {battery_info.get('soc', 'N/A')}%")
                    print(f"⚡ 电压: {battery_info.get('totalVoltage', 'N/A')}V")
                    print(f"🔌 电流: {battery_info.get('totalCurrent', 'N/A')}A")
                    print(f"🌡️ 温度: {battery_info.get('BMSTemp', 'N/A')}°C")
        else:
            print("❌ 数据获取失败")
            
    except Exception as e:
        print(f"程序运行出错: {e}")

if __name__ == "__main__":
    main()
