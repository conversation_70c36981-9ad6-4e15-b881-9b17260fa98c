#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Web监控服务器
提供实时电池数据的Web界面
"""

from flask import Flask, render_template_string, jsonify
import threading
import time
import json
from datetime import datetime
from battery_data_fetcher import BatteryDataFetcher

app = Flask(__name__)

# 全局变量
fetcher = None
latest_data = None
monitoring_active = False

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>锂电池实时监控</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.2em;
        }
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #27ae60;
            margin: 10px 0;
        }
        .status-unit {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .cell-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .cell-item {
            background: #e8f5e8;
            padding: 10px;
            text-align: center;
            border-radius: 5px;
            font-size: 0.9em;
            border: 2px solid #27ae60;
        }
        .cell-item.high {
            background: #ffe8e8;
            border-color: #e74c3c;
        }
        .cell-item.low {
            background: #e8f4fd;
            border-color: #3498db;
        }
        .alert-section {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .alert-title {
            color: #155724;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .alert-item {
            color: #155724;
            margin: 5px 0;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .timestamp {
            text-align: center;
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 20px;
        }
        .loading {
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔋 锂电池实时监控</h1>
            <div class="controls">
                <button class="btn btn-primary" onclick="refreshData()">刷新数据</button>
                <button class="btn btn-success" onclick="toggleAutoRefresh()">
                    <span id="autoRefreshText">开启自动刷新</span>
                </button>
            </div>
        </div>
        
        <div id="dataContainer">
            <div class="loading">正在加载数据...</div>
        </div>
    </div>
    
    <script>
        let autoRefreshInterval = null;
        let autoRefreshActive = false;
        
        function refreshData() {
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateDisplay(data.data);
                    } else {
                        document.getElementById('dataContainer').innerHTML = 
                            '<div class="loading">数据获取失败: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('dataContainer').innerHTML = 
                        '<div class="loading">网络错误: ' + error.message + '</div>';
                });
        }
        
        function updateDisplay(data) {
            const container = document.getElementById('dataContainer');
            
            let html = `
                <div class="status-grid">
                    <div class="status-card">
                        <h3>剩余电量</h3>
                        <div class="status-value">${data.soc}<span class="status-unit">%</span></div>
                    </div>
                    <div class="status-card">
                        <h3>总电压</h3>
                        <div class="status-value">${data.voltage}<span class="status-unit">V</span></div>
                    </div>
                    <div class="status-card">
                        <h3>电流</h3>
                        <div class="status-value">${Math.abs(data.current)}<span class="status-unit">A</span></div>
                    </div>
                    <div class="status-card">
                        <h3>温度</h3>
                        <div class="status-value">${data.temperature}<span class="status-unit">°C</span></div>
                    </div>
                    <div class="status-card">
                        <h3>剩余容量</h3>
                        <div class="status-value">${data.remaining_capacity}<span class="status-unit">Ah</span></div>
                    </div>
                    <div class="status-card">
                        <h3>循环次数</h3>
                        <div class="status-value">${data.cycle_count}<span class="status-unit">次</span></div>
                    </div>
                </div>
                
                <div class="alert-section">
                    <div class="alert-title">电芯电压分布</div>
                    <div class="cell-grid">
            `;
            
            // 生成电芯电压显示
            if (data.cell_voltages && data.cell_voltages.length > 0) {
                const avgVoltage = data.cell_voltages.reduce((a, b) => a + b, 0) / data.cell_voltages.length;
                
                data.cell_voltages.forEach((voltage, index) => {
                    let cellClass = 'cell-item';
                    if (voltage > avgVoltage + 0.01) {
                        cellClass += ' high';
                    } else if (voltage < avgVoltage - 0.01) {
                        cellClass += ' low';
                    }
                    
                    html += `<div class="${cellClass}">电芯${index + 1}<br>${voltage}V</div>`;
                });
            }
            
            html += `
                    </div>
                </div>
                
                <div class="alert-section">
                    <div class="alert-title">系统状态</div>
            `;
            
            // 显示充电状态
            const chargingStatus = data.current < 0 ? '🔌 充电中' : 
                                 data.current > 0 ? '🔋 放电中' : '⏸️ 静置';
            html += `<div class="alert-item">状态: ${chargingStatus}</div>`;
            
            // 显示告警信息
            if (data.alerts && data.alerts.length > 0) {
                data.alerts.forEach(alert => {
                    html += `<div class="alert-item">⚡ ${alert}</div>`;
                });
            }
            
            if (data.protections && data.protections.length > 0) {
                data.protections.forEach(protection => {
                    html += `<div class="alert-item">🚨 ${protection}</div>`;
                });
            }
            
            if ((!data.alerts || data.alerts.length === 0) && 
                (!data.protections || data.protections.length === 0)) {
                html += '<div class="alert-item">✅ 系统正常运行</div>';
            }
            
            html += `
                </div>
                
                <div class="timestamp">
                    电池ID: ${data.battery_id}<br>
                    最后更新: ${new Date(data.timestamp).toLocaleString('zh-CN')}
                </div>
            `;
            
            container.innerHTML = html;
        }
        
        function toggleAutoRefresh() {
            const button = document.getElementById('autoRefreshText');
            
            if (autoRefreshActive) {
                clearInterval(autoRefreshInterval);
                autoRefreshActive = false;
                button.textContent = '开启自动刷新';
            } else {
                autoRefreshInterval = setInterval(refreshData, 30000); // 30秒刷新一次
                autoRefreshActive = true;
                button.textContent = '关闭自动刷新';
                refreshData(); // 立即刷新一次
            }
        }
        
        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/data')
def get_data():
    """获取电池数据API"""
    try:
        if not fetcher:
            return jsonify({'success': False, 'message': '数据获取器未初始化'})
        
        # 获取最新数据
        raw_data = fetcher.get_all_data()
        
        if not raw_data.get('success'):
            return jsonify({'success': False, 'message': '数据获取失败'})
        
        # 解析数据
        real_time = raw_data['real_time_data']
        detail_data = json.loads(real_time['detail'])
        battery_info = json.loads(detail_data['batteryPackageInfo'])
        
        # 获取告警信息
        alerts = []
        protections = []
        if raw_data.get('protection_warnings'):
            result = raw_data['protection_warnings']['result']
            alerts = result.get('alerts', [])
            protections = result.get('protections', [])
        
        # 构建响应数据
        response_data = {
            'timestamp': raw_data['timestamp'],
            'battery_id': detail_data['batteryId'],
            'soc': float(battery_info['soc']),
            'voltage': float(battery_info['totalVoltage']),
            'current': float(battery_info['totalCurrent']),
            'temperature': float(battery_info['BMSTemp']),
            'cell_voltages': [float(v) for v in battery_info['cellVoltageDetail']],
            'cycle_count': int(battery_info['loopTimes']),
            'remaining_capacity': float(battery_info['residualCapacity']),
            'current_capacity': float(battery_info['currentCapacity']),
            'alerts': alerts,
            'protections': protections
        }
        
        return jsonify({'success': True, 'data': response_data})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'})

@app.route('/api/status')
def get_status():
    """获取服务状态"""
    return jsonify({
        'success': True,
        'fetcher_initialized': fetcher is not None,
        'monitoring_active': monitoring_active
    })

def init_fetcher():
    """初始化数据获取器"""
    global fetcher
    try:
        fetcher = BatteryDataFetcher()
        print("✅ 数据获取器初始化成功")
        return True
    except Exception as e:
        print(f"❌ 数据获取器初始化失败: {e}")
        return False

if __name__ == '__main__':
    print("🚀 启动简单Web监控服务器...")
    
    # 初始化数据获取器
    if init_fetcher():
        print("📊 访问地址: http://localhost:5000")
        print("🔄 支持手动和自动刷新")
        print("📱 支持移动设备访问")
        
        # 启动Flask应用
        app.run(host='0.0.0.0', port=5000, debug=False)
    else:
        print("❌ 初始化失败，无法启动服务器")
