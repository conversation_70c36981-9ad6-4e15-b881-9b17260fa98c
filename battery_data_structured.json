{"analysis_info": {"analysis_time": "2025-06-18T10:41:25", "data_source": "微信小程序抓包数据", "total_records": {"battery_basic_info": 18, "real_time_data": 19, "device_parameters": 16, "protection_warnings": 18}}, "battery_basic_info": {"battery_id": "BT2072055010032504140408", "battery_type": "72V55Ah", "client_id": "380074209785", "create_time": "2025-06-16 14:27:56", "create_by": "wanyang_ZZxXlsqAFg", "battery_image": "temp/72V55Ah_1745995372500.png"}, "real_time_status": {"device_info": {"mobile": "380074209785", "heart_beat_time": "250618102355", "location": {"latitude": 22.506921, "longitude": 113.41764, "altitude": 9, "speed": 0, "direction": 164}}, "battery_package": {"total_voltage": 81.7, "total_current": -10.0, "soc": 87, "cell_quantity": 20, "temp_quantity": 2, "bms_temp": 33, "residual_capacity": 46.2, "current_capacity": 53.0, "loop_times": 1, "cell_voltage_detail": [4.087, 4.087, 4.089, 4.088, 4.087, 4.089, 4.09, 4.089, 4.088, 4.09, 4.09, 4.09, 4.091, 4.088, 4.088, 4.088, 4.088, 4.088, 4.089, 4.089], "temp_detail_info": [32.0, 31.0], "voltage_statistics": {"max_voltage": 4.091, "min_voltage": 4.087, "voltage_difference": 0.004, "average_voltage": 4.0885}, "temperature_statistics": {"max_temp": 32.0, "min_temp": 31.0, "temp_difference": 1.0, "average_temp": 31.5}}, "bms_status": {"env_temp": 34, "charge_mos_status": 1, "discharge_mos_status": 1, "total_mileage": 15.2, "total_sum_charge_times": 4250, "total_sum_discharge_time": 2700, "ccid": "89860406192490031343", "iemi": "460042639021943", "dtu": "BMS_F24S3TC_V3_00_17", "bms_sv": "3023", "bms_hv": "2"}}, "protection_warnings": {"current_alerts": ["定时休眠打开", "放电MOS正常闭合", "电池充电中标志", "充电MOS正常闭合"], "current_protections": [], "alert_status": "normal", "protection_status": "normal"}, "device_parameters": {"charge_parameters": {"charge_current_calibration_k_value": 1000, "charging_over_current_protection_value": 45, "charge_over_current_protection_delay": 8, "charging_over_current_alarm_value": 40}, "discharge_parameters": {"discharge_current_calibration_k_value": 1001, "discharge_over_current_1_protection_value": 66, "discharge_over_current_1_protection_delay": 8, "discharge_over_current_2_protection_value": 77, "discharge_over_current_2_protection_delay": 1280, "discharge_over_current_alarm_value": 55}, "short_circuit_protection": {"short_circuit_protection_current": 500, "short_circuit_protection_delay": 406}}, "health_assessment": {"overall_status": "excellent", "battery_health": {"cycle_count": 1, "capacity_retention": 100, "cell_consistency": "excellent", "temperature_control": "normal"}, "charging_status": {"is_charging": true, "charging_current": 10.0, "charging_voltage_trend": "increasing", "soc_trend": "increasing"}, "safety_status": {"protection_triggered": false, "bms_functioning": true, "temperature_normal": true, "voltage_balance": true}, "recommendations": ["继续监控电芯一致性和温度变化", "当前充电参数合理，建议保持", "作为新电池，建议每月检查一次基本参数"]}, "technical_specifications": {"battery_type": "72V55Ah锂电池组", "cell_configuration": "20S (20节串联)", "bms_version": "BMS_F24S3TC_V3_00_17", "communication": "4G网络", "positioning": "GPS定位", "protection_features": ["多级过流保护", "短路保护", "温度保护", "电压保护", "均衡保护"]}, "data_trends": {"soc_history": [84, 85, 87], "voltage_history": [81.1, 81.4, 81.7], "current_history": [-10.0, -10.0, -10.0], "temperature_history": [33, 33, 33], "timestamps": ["250618101544", "250618101848", "250618102355"]}}