
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电池状态报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', <PERSON>l, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center; min-width: 120px; }
        .metric-value { font-size: 1.5em; font-weight: bold; color: #27ae60; }
        .metric-label { color: #7f8c8d; font-size: 0.9em; }
        .status { padding: 15px; margin: 20px 0; border-radius: 8px; }
        .status-normal { background: #d4edda; border-left: 4px solid #28a745; color: #155724; }
        .status-warning { background: #fff3cd; border-left: 4px solid #ffc107; color: #856404; }
        .timestamp { text-align: center; color: #6c757d; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔋 锂电池状态报告</h1>
            <p>电池ID: BT2072055010032504140408</p>
        </div>
        
        <div class="metric">
            <div class="metric-value">96%</div>
            <div class="metric-label">剩余电量</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">83.70V</div>
            <div class="metric-label">总电压</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">10.0A</div>
            <div class="metric-label">电流</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">33°C</div>
            <div class="metric-label">温度</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">51.30Ah</div>
            <div class="metric-label">剩余容量</div>
        </div>
        
        <div class="metric">
            <div class="metric-value">1</div>
            <div class="metric-label">循环次数</div>
        </div>
        
        <div class="status status-normal">
            <strong>系统状态:</strong>
            
            
            <br>⚡ 定时休眠打开<br>⚡ 放电MOS正常闭合<br>⚡ 电池充电中标志<br>⚡ 充电MOS正常闭合
        </div>
        
        <div class="timestamp">
            生成时间: 2025-06-18 10:55:07
        </div>
    </div>
    
    <script>
        // 自动刷新页面
        setTimeout(function() {
            location.reload();
        }, 30000); // 30秒后刷新
    </script>
</body>
</html>
