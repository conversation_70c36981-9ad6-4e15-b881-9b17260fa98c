#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
锂电池监控系统启动器
提供多种监控模式选择
"""

import os
import sys
import time
import threading
import webbrowser
from datetime import datetime
from battery_data_fetcher import BatteryDataFetcher

class BatteryMonitorLauncher:
    def __init__(self):
        """初始化启动器"""
        self.fetcher = None
        self.monitoring_active = False
        
    def init_system(self):
        """初始化系统"""
        print("🔋" + "="*60 + "🔋")
        print("           锂电池实时监控系统")
        print("🔋" + "="*60 + "🔋")
        print()
        
        print("📡 正在初始化系统...")
        
        try:
            self.fetcher = BatteryDataFetcher()
            print("✅ 数据获取器初始化成功")
            
            print("🔗 测试API连接...")
            if self.fetcher.test_connection():
                print("✅ API连接测试成功")
                return True
            else:
                print("❌ API连接测试失败")
                return False
                
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🎛️ 监控模式选择")
        print("="*60)
        print("1. 📊 单次数据查询")
        print("2. 🔄 终端实时监控")
        print("3. 🌐 Web界面监控")
        print("4. 📝 生成HTML报告")
        print("5. 💾 数据库查看")
        print("6. ❌ 退出系统")
        print("="*60)
    
    def single_query(self):
        """单次数据查询"""
        print("\n📊 执行单次数据查询...")
        
        try:
            data = self.fetcher.get_all_data()
            
            if not data.get('success'):
                print("❌ 数据获取失败")
                return
            
            self.display_battery_status(data)
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
    
    def display_battery_status(self, data):
        """显示电池状态"""
        import json
        
        # 解析数据
        real_time = data['real_time_data']
        detail_data = json.loads(real_time['detail'])
        battery_info = json.loads(detail_data['batteryPackageInfo'])
        
        print("\n" + "="*50)
        print("📋 电池状态报告")
        print("="*50)
        
        # 基本信息
        print(f"🆔 电池ID: {detail_data['batteryId']}")
        print(f"📅 查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 主要参数
        soc = float(battery_info['soc'])
        voltage = float(battery_info['totalVoltage'])
        current = float(battery_info['totalCurrent'])
        temperature = float(battery_info['BMSTemp'])
        
        print("📊 主要参数:")
        print(f"  🔋 SOC: {soc:.1f}%")
        print(f"  ⚡ 电压: {voltage:.2f}V")
        print(f"  🔌 电流: {current:.2f}A")
        print(f"  🌡️ 温度: {temperature:.1f}°C")
        print(f"  📦 容量: {battery_info['residualCapacity']}Ah")
        print(f"  🔄 循环: {battery_info['loopTimes']}次")
        
        # 充电状态
        if current < 0:
            status = "🔌 充电中"
        elif current > 0:
            status = "🔋 放电中"
        else:
            status = "⏸️ 静置"
        print(f"  🔄 状态: {status}")
        
        # 电芯信息
        cell_voltages = [float(v) for v in battery_info['cellVoltageDetail']]
        print(f"\n🔬 电芯信息 ({len(cell_voltages)}节):")
        print(f"  📈 最高: {max(cell_voltages):.3f}V")
        print(f"  📉 最低: {min(cell_voltages):.3f}V")
        print(f"  📊 差值: {max(cell_voltages) - min(cell_voltages):.3f}V")
        
        # 告警状态
        alerts = []
        protections = []
        if data.get('protection_warnings'):
            result = data['protection_warnings']['result']
            alerts = result.get('alerts', [])
            protections = result.get('protections', [])
        
        print(f"\n⚠️ 系统状态:")
        if protections:
            for protection in protections:
                print(f"  🚨 {protection}")
        elif alerts:
            print(f"  ⚡ 正常运行 ({len(alerts)}个提示)")
        else:
            print("  ✅ 系统正常")
        
        print("="*50)
    
    def terminal_monitor(self):
        """终端实时监控"""
        print("\n🔄 启动终端实时监控...")
        print("💡 提示: 按 Ctrl+C 停止监控")
        
        try:
            import subprocess
            subprocess.run([sys.executable, "real_time_monitor.py"])
        except KeyboardInterrupt:
            print("\n🛑 监控已停止")
        except Exception as e:
            print(f"❌ 启动终端监控失败: {e}")
    
    def web_monitor(self):
        """Web界面监控"""
        print("\n🌐 启动Web界面监控...")
        
        try:
            # 启动Web服务器
            print("🚀 正在启动Web服务器...")
            print("📊 访问地址: http://localhost:5000")
            
            # 在新线程中启动服务器
            def start_server():
                import subprocess
                subprocess.run([sys.executable, "simple_web_monitor.py"])
            
            server_thread = threading.Thread(target=start_server)
            server_thread.daemon = True
            server_thread.start()
            
            # 等待服务器启动
            time.sleep(3)
            
            # 尝试打开浏览器
            try:
                webbrowser.open('http://localhost:5000')
                print("🚀 已在浏览器中打开监控界面")
            except:
                print("⚠️ 无法自动打开浏览器，请手动访问 http://localhost:5000")
            
            print("💡 提示: 按回车键返回主菜单")
            input()
            
        except Exception as e:
            print(f"❌ 启动Web监控失败: {e}")
    
    def generate_html_report(self):
        """生成HTML报告"""
        print("\n📝 生成HTML报告...")
        
        try:
            import subprocess
            result = subprocess.run([sys.executable, "test_monitor.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ HTML报告生成成功")
                # 查找生成的HTML文件
                import glob
                html_files = glob.glob("battery_report_*.html")
                if html_files:
                    latest_file = max(html_files, key=os.path.getctime)
                    print(f"📄 报告文件: {latest_file}")
                    
                    # 询问是否打开
                    choice = input("是否在浏览器中打开报告? (y/n): ").strip().lower()
                    if choice == 'y':
                        webbrowser.open(f'file://{os.path.abspath(latest_file)}')
                        print("🚀 已在浏览器中打开报告")
            else:
                print("❌ HTML报告生成失败")
                print(result.stderr)
                
        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
    
    def view_database(self):
        """查看数据库"""
        print("\n💾 数据库查看功能...")
        
        try:
            from battery_data_processor import BatteryDataProcessor
            processor = BatteryDataProcessor()
            
            # 获取历史数据
            print("📊 获取最近24小时数据...")
            history = processor.get_historical_data(24)
            
            if history:
                print(f"📈 找到 {len(history)} 条记录")
                print("\n最近10条记录:")
                print("-" * 80)
                print(f"{'时间':<20} {'SOC':<8} {'电压':<8} {'电流':<8} {'温度':<8} {'状态':<8}")
                print("-" * 80)
                
                for record in history[-10:]:
                    timestamp = record['timestamp'][:19]  # 只显示到秒
                    soc = f"{record['soc']:.1f}%"
                    voltage = f"{record['voltage']:.2f}V"
                    current = f"{record['current']:.1f}A"
                    temperature = f"{record['temperature']:.1f}°C"
                    status = "充电" if record['is_charging'] else "放电"
                    
                    print(f"{timestamp:<20} {soc:<8} {voltage:<8} {current:<8} {temperature:<8} {status:<8}")
                
                print("-" * 80)
            else:
                print("📭 数据库中暂无历史数据")
                print("💡 提示: 运行数据处理器后会自动记录数据")
                
        except Exception as e:
            print(f"❌ 查看数据库失败: {e}")
    
    def run(self):
        """运行启动器"""
        # 初始化系统
        if not self.init_system():
            print("❌ 系统初始化失败，无法继续")
            return
        
        # 主循环
        while True:
            try:
                self.show_main_menu()
                choice = input("\n请选择操作 (1-6): ").strip()
                
                if choice == '1':
                    self.single_query()
                elif choice == '2':
                    self.terminal_monitor()
                elif choice == '3':
                    self.web_monitor()
                elif choice == '4':
                    self.generate_html_report()
                elif choice == '5':
                    self.view_database()
                elif choice == '6':
                    print("\n👋 感谢使用锂电池监控系统！")
                    break
                else:
                    print("❌ 无效选择，请重新输入")
                
                if choice != '6':
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户退出系统")
                break
            except Exception as e:
                print(f"\n❌ 系统错误: {e}")
                input("按回车键继续...")

def main():
    """主函数"""
    launcher = BatteryMonitorLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
