#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
电池数据处理器
实时数据解析、验证和存储处理逻辑
"""

import json
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from battery_data_fetcher import BatteryDataFetcher
import logging

@dataclass
class BatteryStatus:
    """电池状态数据类"""
    timestamp: str
    battery_id: str
    soc: float
    voltage: float
    current: float
    temperature: float
    cell_voltages: List[float]
    cell_temps: List[float]
    cycle_count: int
    remaining_capacity: float
    current_capacity: float
    alerts: List[str]
    protections: List[str]
    location: Dict[str, float]
    is_charging: bool
    
class BatteryDataProcessor:
    def __init__(self, db_path='battery_monitor.db'):
        """初始化数据处理器"""
        self.db_path = db_path
        self.fetcher = BatteryDataFetcher()
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.data_callbacks = []
        self.alert_callbacks = []
        self.latest_data = None
        
        # 初始化数据库
        self._init_database()
        
        # 数据验证阈值
        self.validation_rules = {
            'voltage_min': 60.0,
            'voltage_max': 90.0,
            'current_min': -100.0,
            'current_max': 100.0,
            'temperature_min': -20.0,
            'temperature_max': 80.0,
            'soc_min': 0.0,
            'soc_max': 100.0,
            'cell_voltage_min': 2.5,
            'cell_voltage_max': 4.5,
            'cell_voltage_diff_max': 0.5
        }
    
    def _init_database(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建电池状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS battery_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    battery_id TEXT NOT NULL,
                    soc REAL,
                    voltage REAL,
                    current REAL,
                    temperature REAL,
                    cell_voltages TEXT,
                    cell_temps TEXT,
                    cycle_count INTEGER,
                    remaining_capacity REAL,
                    current_capacity REAL,
                    alerts TEXT,
                    protections TEXT,
                    location TEXT,
                    is_charging BOOLEAN,
                    raw_data TEXT
                )
            ''')
            
            # 创建告警记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    battery_id TEXT NOT NULL,
                    alert_type TEXT NOT NULL,
                    alert_message TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    resolved BOOLEAN DEFAULT FALSE
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON battery_status(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_battery_id ON battery_status(battery_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_alert_timestamp ON alert_history(timestamp)')
            
            conn.commit()
            conn.close()
            
            self.logger.info("数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def parse_raw_data(self, raw_data: Dict) -> Optional[BatteryStatus]:
        """解析原始数据为结构化格式"""
        try:
            if not raw_data.get('success') or not raw_data.get('real_time_data'):
                return None
            
            real_time = raw_data['real_time_data']
            detail_data = json.loads(real_time['detail'])
            
            # 解析电池包信息
            battery_info = json.loads(detail_data['batteryPackageInfo'])
            
            # 解析BMS状态信息
            bms_status = json.loads(detail_data['bmsStatusInfo'])
            
            # 获取告警和保护信息
            alerts = []
            protections = []
            if raw_data.get('protection_warnings'):
                result = raw_data['protection_warnings']['result']
                alerts = result.get('alerts', [])
                protections = result.get('protections', [])
            
            # 构建电池状态对象
            status = BatteryStatus(
                timestamp=raw_data['timestamp'],
                battery_id=detail_data['batteryId'],
                soc=float(battery_info['soc']),
                voltage=float(battery_info['totalVoltage']),
                current=float(battery_info['totalCurrent']),
                temperature=float(battery_info['BMSTemp']),
                cell_voltages=[float(v) for v in battery_info['cellVoltageDetail']],
                cell_temps=[float(t) for t in battery_info['tempDetailInfo']],
                cycle_count=int(battery_info['loopTimes']),
                remaining_capacity=float(battery_info['residualCapacity']),
                current_capacity=float(battery_info['currentCapacity']),
                alerts=alerts,
                protections=protections,
                location={
                    'latitude': float(detail_data['latitude']),
                    'longitude': float(detail_data['longitude']),
                    'altitude': float(detail_data['altitude'])
                },
                is_charging=float(battery_info['totalCurrent']) < 0
            )
            
            return status
            
        except (KeyError, ValueError, json.JSONDecodeError) as e:
            self.logger.error(f"数据解析失败: {e}")
            return None
    
    def validate_data(self, status: BatteryStatus) -> List[str]:
        """验证数据有效性，返回验证错误列表"""
        errors = []
        
        # 电压验证
        if not (self.validation_rules['voltage_min'] <= status.voltage <= self.validation_rules['voltage_max']):
            errors.append(f"电压异常: {status.voltage}V")
        
        # 电流验证
        if not (self.validation_rules['current_min'] <= status.current <= self.validation_rules['current_max']):
            errors.append(f"电流异常: {status.current}A")
        
        # 温度验证
        if not (self.validation_rules['temperature_min'] <= status.temperature <= self.validation_rules['temperature_max']):
            errors.append(f"温度异常: {status.temperature}°C")
        
        # SOC验证
        if not (self.validation_rules['soc_min'] <= status.soc <= self.validation_rules['soc_max']):
            errors.append(f"SOC异常: {status.soc}%")
        
        # 电芯电压验证
        for i, voltage in enumerate(status.cell_voltages):
            if not (self.validation_rules['cell_voltage_min'] <= voltage <= self.validation_rules['cell_voltage_max']):
                errors.append(f"电芯{i+1}电压异常: {voltage}V")
        
        # 电芯电压差验证
        if status.cell_voltages:
            voltage_diff = max(status.cell_voltages) - min(status.cell_voltages)
            if voltage_diff > self.validation_rules['cell_voltage_diff_max']:
                errors.append(f"电芯电压差过大: {voltage_diff:.3f}V")
        
        return errors
    
    def save_to_database(self, status: BatteryStatus, raw_data: Dict):
        """保存数据到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO battery_status (
                    timestamp, battery_id, soc, voltage, current, temperature,
                    cell_voltages, cell_temps, cycle_count, remaining_capacity,
                    current_capacity, alerts, protections, location, is_charging, raw_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                status.timestamp,
                status.battery_id,
                status.soc,
                status.voltage,
                status.current,
                status.temperature,
                json.dumps(status.cell_voltages),
                json.dumps(status.cell_temps),
                status.cycle_count,
                status.remaining_capacity,
                status.current_capacity,
                json.dumps(status.alerts),
                json.dumps(status.protections),
                json.dumps(status.location),
                status.is_charging,
                json.dumps(raw_data)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"数据保存失败: {e}")
    
    def save_alert(self, battery_id: str, alert_type: str, message: str, severity: str = 'warning'):
        """保存告警记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO alert_history (timestamp, battery_id, alert_type, alert_message, severity)
                VALUES (?, ?, ?, ?, ?)
            ''', (datetime.now().isoformat(), battery_id, alert_type, message, severity))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"告警保存失败: {e}")
    
    def process_data(self):
        """处理一次数据获取和处理"""
        try:
            # 获取原始数据
            raw_data = self.fetcher.get_all_data()
            
            if not raw_data['success']:
                self.logger.warning("数据获取失败")
                return None
            
            # 解析数据
            status = self.parse_raw_data(raw_data)
            if not status:
                self.logger.warning("数据解析失败")
                return None
            
            # 验证数据
            validation_errors = self.validate_data(status)
            if validation_errors:
                for error in validation_errors:
                    self.logger.warning(f"数据验证警告: {error}")
                    self.save_alert(status.battery_id, 'validation_error', error, 'warning')
            
            # 保存到数据库
            self.save_to_database(status, raw_data)
            
            # 更新最新数据
            self.latest_data = status
            
            # 调用数据回调
            for callback in self.data_callbacks:
                try:
                    callback(status)
                except Exception as e:
                    self.logger.error(f"数据回调执行失败: {e}")
            
            # 检查告警条件
            self._check_alerts(status)
            
            self.logger.info(f"数据处理完成 - SOC: {status.soc}%, 电压: {status.voltage}V, 温度: {status.temperature}°C")
            
            return status
            
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            return None
    
    def _check_alerts(self, status: BatteryStatus):
        """检查告警条件"""
        alerts = []
        
        # 低电量告警
        if status.soc < 20:
            alerts.append(('low_battery', f'电池电量过低: {status.soc}%', 'warning'))
        elif status.soc < 10:
            alerts.append(('critical_battery', f'电池电量严重不足: {status.soc}%', 'critical'))
        
        # 高温告警
        if status.temperature > 50:
            alerts.append(('high_temperature', f'电池温度过高: {status.temperature}°C', 'warning'))
        elif status.temperature > 60:
            alerts.append(('critical_temperature', f'电池温度严重过高: {status.temperature}°C', 'critical'))
        
        # 电芯不一致告警
        if status.cell_voltages:
            voltage_diff = max(status.cell_voltages) - min(status.cell_voltages)
            if voltage_diff > 0.1:
                alerts.append(('cell_imbalance', f'电芯电压不一致: {voltage_diff:.3f}V', 'warning'))
        
        # 保护触发告警
        if status.protections:
            for protection in status.protections:
                alerts.append(('protection_triggered', f'保护触发: {protection}', 'critical'))
        
        # 保存和通知告警
        for alert_type, message, severity in alerts:
            self.save_alert(status.battery_id, alert_type, message, severity)
            
            # 调用告警回调
            for callback in self.alert_callbacks:
                try:
                    callback(alert_type, message, severity)
                except Exception as e:
                    self.logger.error(f"告警回调执行失败: {e}")
    
    def add_data_callback(self, callback):
        """添加数据更新回调"""
        self.data_callbacks.append(callback)
    
    def add_alert_callback(self, callback):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def get_latest_data(self) -> Optional[BatteryStatus]:
        """获取最新数据"""
        return self.latest_data
    
    def get_historical_data(self, hours: int = 24) -> List[Dict]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            start_time = (datetime.now() - timedelta(hours=hours)).isoformat()
            
            cursor.execute('''
                SELECT timestamp, soc, voltage, current, temperature, is_charging
                FROM battery_status
                WHERE timestamp >= ?
                ORDER BY timestamp
            ''', (start_time,))
            
            rows = cursor.fetchall()
            conn.close()
            
            return [
                {
                    'timestamp': row[0],
                    'soc': row[1],
                    'voltage': row[2],
                    'current': row[3],
                    'temperature': row[4],
                    'is_charging': row[5]
                }
                for row in rows
            ]
            
        except Exception as e:
            self.logger.error(f"获取历史数据失败: {e}")
            return []

def main():
    """主函数 - 测试数据处理功能"""
    processor = BatteryDataProcessor()
    
    # 测试数据处理
    print("测试数据处理...")
    status = processor.process_data()
    
    if status:
        print("✅ 数据处理成功")
        print(f"🔋 电池ID: {status.battery_id}")
        print(f"⚡ SOC: {status.soc}%")
        print(f"🔌 电压: {status.voltage}V")
        print(f"🌡️ 温度: {status.temperature}°C")
        print(f"🔄 充电状态: {'充电中' if status.is_charging else '放电中'}")
    else:
        print("❌ 数据处理失败")

if __name__ == "__main__":
    main()
