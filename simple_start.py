#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 简单启动器
Simple Launcher for Wanyang Battery Monitoring
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def show_menu():
    """显示菜单"""
    print("🔋 万洋锂电池监控系统")
    print("=" * 40)
    print("1. 🌐 启动手机端Web监控")
    print("2. 📊 启动控制台监控")
    print("0. ❌ 退出")
    print("=" * 40)

def start_web_server():
    """启动Web服务器"""
    print("🌐 启动手机端Web监控...")
    print("📱 启动后可通过以下地址访问:")
    print("   • 本机: http://localhost:5000")
    print("   • 手机: http://你的电脑IP:5000")
    print("🔄 数据每5秒自动更新")
    print("🎮 支持远程控制功能")
    print("\n⏳ 正在启动服务器...")
    
    try:
        # 启动Web服务器
        subprocess.run([sys.executable, "mobile_web_server.py"])
    except KeyboardInterrupt:
        print("\n👋 Web服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def start_console_monitor():
    """启动控制台监控"""
    print("📊 启动控制台监控...")
    print("🖥️ 美观的文字版数据显示")
    print("📡 包含API接口数据")
    print("🔄 每5秒自动刷新")
    print("\n⏳ 正在启动监控...")
    
    try:
        # 启动控制台监控
        subprocess.run([sys.executable, "console_monitor.py"])
    except KeyboardInterrupt:
        print("\n👋 控制台监控已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    while True:
        try:
            show_menu()
            choice = input("请选择 (0-2): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                start_web_server()
            elif choice == "2":
                start_console_monitor()
            else:
                print("❌ 无效选择，请重新输入")
            
            print("\n" + "-" * 40)
            
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
