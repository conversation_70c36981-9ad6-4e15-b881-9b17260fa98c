
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>锂电池状态监控面板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            color: #7f8c8d;
            margin: 10px 0 0 0;
            font-size: 1.1em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.2em;
        }
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #27ae60;
            margin: 10px 0;
        }
        .status-unit {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .chart-container {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .chart-title {
            text-align: center;
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 1.5em;
        }
        .alert-section {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .alert-title {
            color: #155724;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .alert-item {
            color: #155724;
            margin: 5px 0;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .warning .alert-title, .warning .alert-item {
            color: #856404;
        }
        .cell-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .cell-item {
            background: #e8f5e8;
            padding: 10px;
            text-align: center;
            border-radius: 5px;
            font-size: 0.9em;
            border: 2px solid #27ae60;
        }
        .cell-item.high {
            background: #ffe8e8;
            border-color: #e74c3c;
        }
        .cell-item.low {
            background: #e8f4fd;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔋 锂电池监控面板</h1>
            <p>电池ID: BT2072055010032504140408 | 类型: 72V55Ah | 更新时间: 2025-06-18 10:43:07</p>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>剩余电量 (SOC)</h3>
                <div class="status-value">87<span class="status-unit">%</span></div>
            </div>
            <div class="status-card">
                <h3>总电压</h3>
                <div class="status-value">81.7<span class="status-unit">V</span></div>
            </div>
            <div class="status-card">
                <h3>充电电流</h3>
                <div class="status-value">10.0<span class="status-unit">A</span></div>
            </div>
            <div class="status-card">
                <h3>BMS温度</h3>
                <div class="status-value">33<span class="status-unit">°C</span></div>
            </div>
            <div class="status-card">
                <h3>循环次数</h3>
                <div class="status-value">1<span class="status-unit">次</span></div>
            </div>
            <div class="status-card">
                <h3>剩余容量</h3>
                <div class="status-value">46.2<span class="status-unit">Ah</span></div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">SOC和电压变化趋势</div>
            <canvas id="trendChart" width="400" height="200"></canvas>
        </div>
        
        <div class="chart-container">
            <div class="chart-title">电芯电压分布 (20节电芯)</div>
            <div class="cell-grid">
                <div class="cell-item">电芯1<br>4.058V</div><div class="cell-item">电芯2<br>4.058V</div><div class="cell-item">电芯3<br>4.06V</div><div class="cell-item">电芯4<br>4.059V</div><div class="cell-item">电芯5<br>4.058V</div><div class="cell-item">电芯6<br>4.06V</div><div class="cell-item">电芯7<br>4.061V</div><div class="cell-item">电芯8<br>4.06V</div><div class="cell-item">电芯9<br>4.059V</div><div class="cell-item">电芯10<br>4.061V</div><div class="cell-item">电芯11<br>4.061V</div><div class="cell-item">电芯12<br>4.061V</div><div class="cell-item high">电芯13<br>4.062V</div><div class="cell-item">电芯14<br>4.059V</div><div class="cell-item">电芯15<br>4.059V</div><div class="cell-item">电芯16<br>4.059V</div><div class="cell-item">电芯17<br>4.059V</div><div class="cell-item">电芯18<br>4.059V</div><div class="cell-item">电芯19<br>4.06V</div><div class="cell-item">电芯20<br>4.06V</div>
            </div>
            <canvas id="cellChart" width="400" height="200"></canvas>
        </div>
        
        <div class="alert-section">
            <div class="alert-title">✅ 系统状态正常</div>
            <div class="alert-item">• 定时休眠功能已开启</div>
            <div class="alert-item">• 充放电MOS管正常工作</div>
            <div class="alert-item">• 电池正在充电中</div>
            <div class="alert-item">• 无保护触发，系统运行正常</div>
        </div>
        
        <div class="alert-section warning">
            <div class="alert-title">⚡ 充电状态</div>
            <div class="alert-item">• 当前充电电流: 10.0A</div>
            <div class="alert-item">• 电池电量从84%上升至87%</div>
            <div class="alert-item">• 充电电压正常上升</div>
        </div>
    </div>
    
    <script>
        // SOC和电压趋势图
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: ['时间点1', '时间点2', '时间点3'],
                datasets: [{
                    label: 'SOC (%)',
                    data: [84, 85, 87],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: '电压 (V)',
                    data: [81.1, 81.4, 81.7],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'SOC (%)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '电压 (V)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
        
        // 电芯电压分布图
        const cellCtx = document.getElementById('cellChart').getContext('2d');
        new Chart(cellCtx, {
            type: 'bar',
            data: {
                labels: ['电芯1', '电芯2', '电芯3', '电芯4', '电芯5', '电芯6', '电芯7', '电芯8', '电芯9', '电芯10', '电芯11', '电芯12', '电芯13', '电芯14', '电芯15', '电芯16', '电芯17', '电芯18', '电芯19', '电芯20'],
                datasets: [{
                    label: '电芯电压 (V)',
                    data: [4.058, 4.058, 4.06, 4.059, 4.058, 4.06, 4.061, 4.06, 4.059, 4.061, 4.061, 4.061, 4.062, 4.059, 4.059, 4.059, 4.059, 4.059, 4.06, 4.06],
                    backgroundColor: 'rgba(39, 174, 96, 0.6)',
                    borderColor: '#27ae60',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 4.05,
                        max: 4.10,
                        title: {
                            display: true,
                            text: '电压 (V)'
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
