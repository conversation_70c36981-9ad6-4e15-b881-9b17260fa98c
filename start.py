#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 项目启动器
Project Launcher for Wanyang Battery Monitoring System
"""

import os
import sys
import webbrowser
import time
from pathlib import Path

def show_project_structure():
    """显示项目结构"""
    print("🏗️ 万洋锂电池监控系统 - 项目结构")
    print("=" * 60)
    
    structure = """
📁 xdczb/                          # 项目根目录
├── 📁 src/                        # 源代码目录
│   ├── 🐍 enhanced_battery_monitor.py    # 增强版监控系统 (主程序)
│   ├── 🐍 battery_data_fetcher.py        # 数据获取器
│   ├── 🐍 battery_data_processor.py      # 数据处理器
│   ├── 🐍 battery_data_analyzer.py       # 数据分析器
│   ├── 🐍 new_data_analyzer.py           # 新抓包数据分析器
│   ├── 🐍 real_time_monitor.py           # 实时监控器
│   ├── 🐍 web_server.py                  # Web服务器
│   ├── 🐍 api_analyzer.py                # API分析器
│   ├── 🐍 battery_visualization.py       # 数据可视化
│   ├── 🐍 demo_enhanced_system.py        # 演示脚本
│   └── 🐍 test_monitor.py                # 测试脚本
│
├── 📁 web/                        # Web界面目录
│   ├── 🌐 wanyang_battery_app.html       # 小程序复刻版 (推荐)
│   ├── 🌐 battery_dashboard.html         # 仪表板界面
│   ├── 🌐 battery_report.html            # 报告界面
│   └── 📁 templates/                     # 模板文件
│       ├── 🌐 enhanced_monitor.html      # 增强版监控界面
│       └── 🌐 monitor.html               # 基础监控界面
│
├── 📁 data/                       # 数据目录
│   ├── 📊 万洋锂电池抓包数据.json         # 原始抓包数据
│   ├── 📊 battery_data_structured.json   # 结构化数据
│   ├── 📊 api_config.json               # API配置
│   └── 🗄️ battery_monitor.db            # 数据库文件
│
├── 📁 docs/                       # 文档目录
│   ├── 📝 README.md                     # 项目说明
│   ├── 📝 SYSTEM_SUMMARY.md             # 系统总结
│   └── 📝 battery_analysis_summary.md   # 分析报告
│
├── 📁 logs/                       # 日志目录
│   └── 📋 *.log                         # 系统日志
│
├── 📁 config/                     # 配置目录
│   └── ⚙️ (配置文件)
│
├── 📁 其他/                       # 其他文件
│   ├── 📁 __pycache__/                  # Python缓存
│   └── 📄 抓包数据.txt                   # 原始抓包文本
│
└── 🚀 start.py                    # 项目启动器 (当前文件)
    """
    
    print(structure)
    print("=" * 60)

def show_startup_options():
    """显示启动选项"""
    print("\n🚀 启动选项:")
    print("1. 🌐 查看小程序复刻版界面 (推荐)")
    print("2. 🖥️ 启动Web监控系统")
    print("3. 📊 启动增强版监控系统")
    print("4. 🔍 运行数据分析")
    print("5. 🧪 运行演示程序")
    print("6. 📋 查看项目文档")
    print("0. ❌ 退出")

def launch_web_interface():
    """启动Web界面"""
    print("\n🌐 启动小程序复刻版界面...")
    
    web_file = Path("web/wanyang_battery_app.html")
    if web_file.exists():
        file_url = f"file:///{web_file.absolute().as_posix()}"
        print(f"📱 打开文件: {file_url}")
        webbrowser.open(file_url)
        print("✅ 界面已在浏览器中打开")
        return True
    else:
        print("❌ 找不到Web界面文件")
        return False

def launch_web_server():
    """启动Web服务器"""
    print("\n🖥️ 启动Web监控系统...")
    
    try:
        os.chdir("src")
        os.system("python web_server.py")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def launch_enhanced_monitor():
    """启动增强版监控系统"""
    print("\n📊 启动增强版监控系统...")
    
    try:
        os.chdir("src")
        os.system("python enhanced_battery_monitor.py")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_data_analysis():
    """运行数据分析"""
    print("\n🔍 运行数据分析...")
    
    try:
        os.chdir("src")
        os.system("python new_data_analyzer.py")
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def run_demo():
    """运行演示程序"""
    print("\n🧪 运行演示程序...")
    
    try:
        os.chdir("src")
        os.system("python demo_enhanced_system.py")
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def show_documentation():
    """显示项目文档"""
    print("\n📋 项目文档:")
    
    docs = [
        ("README.md", "项目说明文档"),
        ("SYSTEM_SUMMARY.md", "系统总结文档"),
        ("battery_analysis_summary.md", "电池分析报告")
    ]
    
    for doc, desc in docs:
        doc_path = Path(f"docs/{doc}")
        if doc_path.exists():
            print(f"✅ {doc} - {desc}")
        else:
            print(f"❌ {doc} - {desc} (文件不存在)")

def main():
    """主函数"""
    print("🔋 万洋锂电池监控系统")
    print("Wanyang Battery Monitoring System")
    print("=" * 60)
    
    # 显示项目结构
    show_project_structure()
    
    while True:
        show_startup_options()
        
        try:
            choice = input("\n请选择启动选项 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                launch_web_interface()
            elif choice == "2":
                launch_web_server()
            elif choice == "3":
                launch_enhanced_monitor()
            elif choice == "4":
                run_data_analysis()
            elif choice == "5":
                run_demo()
            elif choice == "6":
                show_documentation()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        print("\n" + "-" * 40)

if __name__ == "__main__":
    main()
