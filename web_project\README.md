# 🔋 万洋锂电池监控系统 - Web版本

一个完全复刻小程序界面的锂电池实时监控Web应用，专为手机端优化设计。

## ✨ 功能特点

- 🎨 **完美复刻** - 完全复刻小程序界面设计
- 📱 **手机优化** - 专为手机端优化的响应式设计
- ⚡ **实时监控** - 电池SOC、电压、电流、温度实时显示
- 🔋 **电芯监控** - 20节电芯电压分布可视化
- 🎮 **远程控制** - 支持终端复位、充放电控制、蜂鸣器控制
- 🔄 **自动刷新** - 数据每3秒自动更新
- 📊 **状态监控** - 系统保护状态实时显示

## 📁 项目结构

```
web_project/
├── app.py              # Flask主应用
├── start.py            # 启动脚本
├── requirements.txt    # 依赖列表
├── README.md          # 项目说明
├── web/               # Web相关文件
│   ├── templates/     # HTML模板
│   │   └── index.html # 主页面模板
│   └── static/        # 静态资源（CSS/JS/图片）
└── other/             # 其他文件
```

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

```bash
python start.py
```

### 方法二：直接启动

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python app.py
```

## 📱 访问方式

启动成功后，可通过以下方式访问：

- **本机访问**: http://localhost:5000
- **手机访问**: http://你的电脑IP:5000

## 🎮 控制功能

Web界面提供以下控制功能：

- 🔄 **终端复位** - 重启电池管理系统
- 🔌 **允许充电** - 开启充电功能
- 🔊 **开启蜂鸣** - 激活蜂鸣器报警
- ⚡ **禁止放电** - 关闭放电功能

## 📊 监控数据

### 主要参数
- 剩余电量 (SOC)
- 总电压
- 总电流
- 温度
- 剩余容量
- 循环次数

### 电芯监控
- 20节电芯电压实时显示
- 最高/最低电压标记
- 电压分布可视化

### 系统状态
- 静置状态
- 充电状态
- MOS管状态
- 保护状态

## 🔧 技术栈

- **后端**: Flask (Python)
- **前端**: HTML5 + CSS3 + JavaScript
- **设计**: 响应式设计，手机端优化
- **数据**: JSON API接口

## 📝 API接口

### 获取数据
```
GET /api/data
```

### 发送控制命令
```
POST /api/command
Content-Type: application/json

{
    "command": "reset_terminal"
}
```

## 🎨 界面特色

- 🎨 **渐变背景** - 美观的紫色渐变背景
- 📊 **卡片设计** - 现代化的卡片式布局
- 🔋 **电池图标** - 自定义电池状态图标
- 🌈 **状态指示** - 彩色状态指示器
- 💫 **动画效果** - 流畅的交互动画
- 📱 **触控优化** - 适合手机触控操作

## 🔄 自动更新

- 数据每3秒自动刷新
- 支持手动刷新
- 可开启/关闭自动刷新
- 实时连接状态显示

## 🛠️ 开发说明

项目采用标准Flask结构，便于扩展和维护：

- `app.py` - 主应用文件，包含路由和业务逻辑
- `web/templates/` - HTML模板文件
- `web/static/` - 静态资源文件
- `other/` - 其他相关文件

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**万洋锂电池监控系统** - 让电池监控更简单、更直观！ 🔋✨
