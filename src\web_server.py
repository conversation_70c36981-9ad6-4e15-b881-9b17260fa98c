#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务器
提供实时监控界面和API接口
"""

from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import threading
import time
import json
import logging
from datetime import datetime
from battery_data_processor import BatteryDataProcessor

# 配置日志
logging.basicConfig(level=logging.INFO)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'battery_monitor_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
processor = None
monitoring_thread = None
monitoring_active = False

@app.route('/')
def index():
    """主页"""
    return render_template('monitor.html')

@app.route('/api/latest')
def get_latest_data():
    """获取最新数据API"""
    if processor and processor.get_latest_data():
        status = processor.get_latest_data()
        return jsonify({
            'success': True,
            'data': {
                'timestamp': status.timestamp,
                'battery_id': status.battery_id,
                'soc': status.soc,
                'voltage': status.voltage,
                'current': status.current,
                'temperature': status.temperature,
                'cell_voltages': status.cell_voltages,
                'cell_temps': status.cell_temps,
                'cycle_count': status.cycle_count,
                'remaining_capacity': status.remaining_capacity,
                'current_capacity': status.current_capacity,
                'alerts': status.alerts,
                'protections': status.protections,
                'location': status.location,
                'is_charging': status.is_charging
            }
        })
    else:
        return jsonify({'success': False, 'message': '暂无数据'})

@app.route('/api/history')
def get_history_data():
    """获取历史数据API"""
    hours = request.args.get('hours', 24, type=int)
    if processor:
        data = processor.get_historical_data(hours)
        return jsonify({'success': True, 'data': data})
    else:
        return jsonify({'success': False, 'message': '数据处理器未初始化'})

@app.route('/api/start_monitoring')
def start_monitoring():
    """开始监控API"""
    global monitoring_active, monitoring_thread
    
    if not monitoring_active:
        monitoring_active = True
        monitoring_thread = threading.Thread(target=monitoring_loop)
        monitoring_thread.daemon = True
        monitoring_thread.start()
        return jsonify({'success': True, 'message': '监控已开始'})
    else:
        return jsonify({'success': False, 'message': '监控已在运行中'})

@app.route('/api/stop_monitoring')
def stop_monitoring():
    """停止监控API"""
    global monitoring_active
    monitoring_active = False
    return jsonify({'success': True, 'message': '监控已停止'})

@app.route('/api/status')
def get_monitoring_status():
    """获取监控状态API"""
    return jsonify({
        'success': True,
        'monitoring_active': monitoring_active,
        'has_data': processor.get_latest_data() is not None if processor else False
    })

def monitoring_loop():
    """监控循环"""
    global monitoring_active
    
    while monitoring_active:
        try:
            if processor:
                status = processor.process_data()
                if status:
                    # 通过WebSocket推送数据到前端
                    socketio.emit('battery_data', {
                        'timestamp': status.timestamp,
                        'battery_id': status.battery_id,
                        'soc': status.soc,
                        'voltage': status.voltage,
                        'current': status.current,
                        'temperature': status.temperature,
                        'cell_voltages': status.cell_voltages,
                        'cell_temps': status.cell_temps,
                        'cycle_count': status.cycle_count,
                        'remaining_capacity': status.remaining_capacity,
                        'current_capacity': status.current_capacity,
                        'alerts': status.alerts,
                        'protections': status.protections,
                        'location': status.location,
                        'is_charging': status.is_charging
                    })
                    
                    app.logger.info(f"数据推送完成 - SOC: {status.soc}%")
            
            # 等待30秒后再次获取数据
            time.sleep(30)
            
        except Exception as e:
            app.logger.error(f"监控循环出错: {e}")
            time.sleep(5)

def on_data_update(status):
    """数据更新回调"""
    # 通过WebSocket推送数据
    socketio.emit('battery_data', {
        'timestamp': status.timestamp,
        'battery_id': status.battery_id,
        'soc': status.soc,
        'voltage': status.voltage,
        'current': status.current,
        'temperature': status.temperature,
        'cell_voltages': status.cell_voltages,
        'cell_temps': status.cell_temps,
        'is_charging': status.is_charging
    })

def on_alert(alert_type, message, severity):
    """告警回调"""
    # 通过WebSocket推送告警
    socketio.emit('battery_alert', {
        'type': alert_type,
        'message': message,
        'severity': severity,
        'timestamp': datetime.now().isoformat()
    })

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    app.logger.info('客户端已连接')
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开处理"""
    app.logger.info('客户端已断开')

@socketio.on('request_latest_data')
def handle_request_latest_data():
    """处理获取最新数据请求"""
    if processor and processor.get_latest_data():
        status = processor.get_latest_data()
        emit('battery_data', {
            'timestamp': status.timestamp,
            'battery_id': status.battery_id,
            'soc': status.soc,
            'voltage': status.voltage,
            'current': status.current,
            'temperature': status.temperature,
            'cell_voltages': status.cell_voltages,
            'cell_temps': status.cell_temps,
            'cycle_count': status.cycle_count,
            'remaining_capacity': status.remaining_capacity,
            'current_capacity': status.current_capacity,
            'alerts': status.alerts,
            'protections': status.protections,
            'location': status.location,
            'is_charging': status.is_charging
        })

def init_processor():
    """初始化数据处理器"""
    global processor
    try:
        processor = BatteryDataProcessor()
        processor.add_data_callback(on_data_update)
        processor.add_alert_callback(on_alert)
        app.logger.info("数据处理器初始化成功")
        return True
    except Exception as e:
        app.logger.error(f"数据处理器初始化失败: {e}")
        return False

if __name__ == '__main__':
    # 初始化数据处理器
    if init_processor():
        print("🚀 启动电池监控Web服务器...")
        print("📊 访问地址: http://localhost:5000")
        print("🔄 WebSocket实时通信已启用")
        
        # 启动Flask应用
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
    else:
        print("❌ 数据处理器初始化失败，无法启动服务器")
