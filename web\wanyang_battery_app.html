<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万洋智行 - 锂电池监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: #f5f7fa;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            border-radius: 0 0 20px 20px;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .main-display {
            background: white;
            margin: 20px;
            border-radius: 20px;
            padding: 30px 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .soc-container {
            position: relative;
            margin-bottom: 25px;
        }

        .soc-circle {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: conic-gradient(from -90deg, #4CAF50 0%, #4CAF50 var(--percentage), #e8f5e9 var(--percentage), #e8f5e9 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            position: relative;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
        }

        .soc-circle::before {
            content: '';
            width: 110px;
            height: 110px;
            background: white;
            border-radius: 50%;
            position: absolute;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .soc-text {
            font-size: 32px;
            font-weight: bold;
            color: #2e7d32;
            z-index: 1;
        }

        .soc-label {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }

        .battery-status {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 25px;
        }

        .status-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .status-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
        }

        .battery-info-card {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            margin: 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(30, 60, 114, 0.3);
        }

        .battery-code {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .update-time {
            font-size: 12px;
            opacity: 0.8;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            font-size: 11px;
            opacity: 0.8;
            margin-bottom: 3px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 600;
        }

        .tabs {
            display: flex;
            background: white;
            margin: 0 20px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
            transition: all 0.3s;
            font-weight: 500;
        }

        .tab.active {
            color: #1e3c72;
            background: #f8f9fa;
            font-weight: 600;
        }

        .tab-content {
            display: none;
            padding: 20px;
            margin-bottom: 80px;
        }

        .tab-content.active {
            display: block;
        }

        .control-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e9ecef;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .control-btn {
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(30, 60, 114, 0.4);
        }

        .control-btn:active {
            transform: translateY(0);
        }

        .control-btn:disabled {
            background: #adb5bd;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .current-control {
            margin-top: 20px;
        }

        .current-label {
            font-size: 14px;
            color: #495057;
            margin-bottom: 10px;
            font-weight: 500;
        }

        .current-display {
            font-size: 24px;
            font-weight: bold;
            color: #1e3c72;
            text-align: center;
            margin-bottom: 15px;
        }

        .current-buttons {
            display: flex;
            gap: 8px;
        }

        .current-btn {
            flex: 1;
            padding: 12px;
            border: 2px solid #1e3c72;
            border-radius: 10px;
            background: white;
            color: #1e3c72;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .current-btn.active {
            background: #1e3c72;
            color: white;
            box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
        }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .detail-card {
            background: white;
            padding: 18px;
            border-radius: 12px;
            border-left: 4px solid #1e3c72;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .detail-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        }

        .footer-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: none;
            border: none;
            color: #6c757d;
            font-size: 12px;
            cursor: pointer;
            padding: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .footer-btn.active {
            color: #1e3c72;
            font-weight: 600;
        }

        .footer-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
        }
        .cell-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin-top: 20px;
        }

        .cell-item {
            background: white;
            padding: 12px 8px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .cell-item.high {
            border-color: #ff9800;
            background: #fff8e1;
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
        }

        .cell-item.low {
            border-color: #2196f3;
            background: #e3f2fd;
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        .cell-number {
            font-size: 10px;
            color: #6c757d;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .cell-voltage {
            font-size: 13px;
            font-weight: bold;
            color: #495057;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e9ecef;
            border-top: 4px solid #1e3c72;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
            font-weight: 500;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
            font-weight: 500;
        }

        .alert-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .alert-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .alert-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #856404;
        }

        .alert-text {
            color: #856404;
            font-size: 14px;
            font-weight: 500;
        }

        .map-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }

        .temperature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .temp-card {
            background: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .temp-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .temp-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }

        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        .status-card.charging {
            border-left: 4px solid #4CAF50;
            background: #f1f8e9;
        }

        .status-card.normal {
            border-left: 4px solid #2196F3;
            background: #e3f2fd;
        }

        .status-card.excellent {
            border-left: 4px solid #ff9800;
            background: #fff8e1;
        }
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>万洋智行</h1>
            <div class="header-subtitle">锂电池智能监控系统</div>
        </div>

        <!-- 主显示区域 -->
        <div class="main-display">
            <div class="soc-container">
                <div class="soc-circle" id="socCircle">
                    <div class="soc-text" id="socText">99%</div>
                </div>
                <div class="soc-label">预计充满时间 0小时4分钟</div>
            </div>

            <div class="battery-status">
                <div class="status-item">
                    <div class="status-label">充电电流</div>
                    <div class="status-value" id="chargeCurrent">10A</div>
                </div>
                <div class="status-item">
                    <div class="status-label">放电状态</div>
                    <div class="status-value" id="dischargeStatus">关</div>
                </div>
                <div class="status-item">
                    <div class="status-label">均衡保护</div>
                    <div class="status-value" id="balanceStatus">开</div>
                </div>
                <div class="status-item">
                    <div class="status-label">电池状态</div>
                    <div class="status-value" id="batteryStatus">充电</div>
                </div>
            </div>
        </div>

        <!-- 电池信息卡片 -->
        <div class="battery-info-card">
            <div class="battery-code" id="batteryCode">电池编号: BT2072055010032504140408</div>
            <div class="update-time" id="updateTime">更新时间: 2025年06月18日11时02分</div>

            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">总电压</div>
                    <div class="info-value" id="totalVoltage">84.10v</div>
                </div>
                <div class="info-item">
                    <div class="info-label">电流</div>
                    <div class="info-value" id="totalCurrent">-10.00A</div>
                </div>
                <div class="info-item">
                    <div class="info-label">功率</div>
                    <div class="info-value" id="totalPower">-841.00w</div>
                </div>
                <div class="info-item">
                    <div class="info-label">MOS温度</div>
                    <div class="info-value" id="mosTemp">34°c</div>
                </div>
                <div class="info-item">
                    <div class="info-label">环境温度</div>
                    <div class="info-value" id="envTemp">35°c</div>
                </div>
                <div class="info-item">
                    <div class="info-label">电芯温度</div>
                    <div class="info-value" id="cellTemp">33°c/32°c</div>
                </div>
                <div class="info-item">
                    <div class="info-label">GPS预估总里程</div>
                    <div class="info-value" id="totalMileage">15.2km</div>
                </div>
                <div class="info-item">
                    <div class="info-label">压差</div>
                    <div class="info-value" id="voltageDiff">0.005v</div>
                </div>
            </div>
        </div>

        <!-- 保护信息和告警信息 -->
        <div class="battery-info-card">
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">保护信息</div>
                    <div class="info-value">暂无</div>
                </div>
                <div class="info-item">
                    <div class="info-label">告警信息</div>
                    <div class="info-value">🟡 定时休眠打开</div>
                </div>
            </div>
            <div class="info-grid" style="margin-top: 10px;">
                <div class="info-item">
                    <div class="info-value">🟢 放电MOS正常闭合</div>
                </div>
                <div class="info-item">
                    <div class="info-value">🔵 电池充电中标志</div>
                </div>
                <div class="info-item">
                    <div class="info-value">🟠 充电MOS正常闭合</div>
                </div>
            </div>
        </div>

        <!-- 标签页 -->
        <div class="tabs">
            <button class="tab active" onclick="switchTab('home')">首页</button>
            <button class="tab" onclick="switchTab('details')">详情</button>
            <button class="tab" onclick="switchTab('cells')">电芯</button>
            <button class="tab" onclick="switchTab('control')">控制</button>
        </div>

        <!-- 首页内容 -->
        <div id="home" class="tab-content active">
            <div class="control-section">
                <div class="section-title">终端复位</div>
                <div class="control-buttons">
                    <button class="control-btn" onclick="sendCommand('reset')">终端复位</button>
                </div>
            </div>
        </div>

        <!-- 详情页内容 -->
        <div id="details" class="tab-content">
            <div class="control-section">
                <div class="section-title">参数</div>
                <div class="details-grid">
                    <div class="detail-card">
                        <div class="detail-label">GPS预估总里程</div>
                        <div class="detail-value" id="detailMileage">15.2km</div>
                    </div>
                    <div class="detail-card">
                        <div class="detail-label">速度</div>
                        <div class="detail-value" id="detailSpeed">0km/h</div>
                    </div>
                    <div class="detail-card">
                        <div class="detail-label">循环次数</div>
                        <div class="detail-value" id="detailCycles">1次</div>
                    </div>
                    <div class="detail-card">
                        <div class="detail-label">平均电压</div>
                        <div class="detail-value" id="detailAvgVoltage">4.209v</div>
                    </div>
                    <div class="detail-card">
                        <div class="detail-label">最高电压</div>
                        <div class="detail-value" id="detailMaxVoltage">4.212v</div>
                    </div>
                    <div class="detail-card">
                        <div class="detail-label">最低电压</div>
                        <div class="detail-value" id="detailMinVoltage">4.207v</div>
                    </div>
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">温度</div>
                <div class="temperature-grid">
                    <div class="temp-card">
                        <div class="temp-label">MOS温度</div>
                        <div class="temp-value" id="detailMosTemp">34°c</div>
                    </div>
                    <div class="temp-card">
                        <div class="temp-label">环境温度</div>
                        <div class="temp-value" id="detailEnvTemp">35°c</div>
                    </div>
                    <div class="temp-card">
                        <div class="temp-label">电芯温度</div>
                        <div class="temp-value" id="detailCellTemp">33°c/32°c</div>
                    </div>
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">状态</div>
                <div class="status-grid">
                    <div class="status-card charging">
                        <div class="temp-label">电池状态</div>
                        <div class="temp-value">充电</div>
                    </div>
                    <div class="status-card normal">
                        <div class="temp-label">告警状态</div>
                        <div class="temp-value">正常</div>
                    </div>
                    <div class="status-card excellent">
                        <div class="temp-label">GSM信号</div>
                        <div class="temp-value">优 (23)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 电芯页内容 -->
        <div id="cells" class="tab-content">
            <div class="control-section">
                <div class="section-title">电芯电压 (20节)</div>
                <div class="cell-grid" id="cellGrid">
                    <!-- 电芯数据将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">电池定位</div>
                <div class="map-container">
                    <div>
                        <div style="font-size: 16px; margin-bottom: 10px;">📍 当前位置</div>
                        <div style="font-size: 14px;">纬度: 22.506917</div>
                        <div style="font-size: 14px;">经度: 113.417640</div>
                        <div style="font-size: 12px; margin-top: 10px; color: #999;">地图功能需要集成第三方地图服务</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 控制页内容 -->
        <div id="control" class="tab-content">
            <div class="control-section">
                <div class="section-title">蜂鸣器控制</div>
                <div class="control-buttons">
                    <button class="control-btn" onclick="sendCommand('buzzer_on')">开启蜂鸣</button>
                    <button class="control-btn" onclick="sendCommand('buzzer_off')">关闭蜂鸣</button>
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">放电控制</div>
                <div class="control-buttons">
                    <button class="control-btn" onclick="sendCommand('allow_discharge')">允许放电</button>
                    <button class="control-btn" onclick="sendCommand('forbid_discharge')">禁止放电</button>
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">充电控制</div>
                <div class="control-buttons">
                    <button class="control-btn" onclick="sendCommand('allow_charge')">允许充电</button>
                    <button class="control-btn" onclick="sendCommand('forbid_charge')">禁止充电</button>
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">控制放电流</div>
                <div class="current-label">当前放电流</div>
                <div class="current-display" id="currentDisplay">60A</div>
                <div class="current-buttons">
                    <button class="current-btn active" onclick="setDischargeCurrent(60)">60A</button>
                    <button class="current-btn" onclick="setDischargeCurrent(80)">80A</button>
                    <button class="current-btn" onclick="setDischargeCurrent(100)">100A</button>
                </div>
            </div>

            <div class="control-section">
                <div class="section-title">其他操作</div>
                <div class="control-buttons">
                    <button class="control-btn" onclick="showDetails()">详情</button>
                    <button class="control-btn" onclick="unbindBattery()">解绑</button>
                    <button class="control-btn" onclick="switchBattery()">切换电池</button>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="footer">
            <button class="footer-btn active" onclick="switchTab('home')">
                <div class="footer-icon">🏠</div>
                <div>首页</div>
            </button>
            <button class="footer-btn" onclick="switchTab('details')">
                <div class="footer-icon">⚡</div>
                <div>详情</div>
            </button>
            <button class="footer-btn" onclick="switchTab('cells')">
                <div class="footer-icon">🔋</div>
                <div>电芯</div>
            </button>
            <button class="footer-btn" onclick="switchTab('control')">
                <div class="footer-icon">👤</div>
                <div>我的</div>
            </button>
        </div>
    </div>

    <script>
        // 模拟电池数据
        let batteryData = {
            soc: 99,
            totalVoltage: 84.10,
            totalCurrent: -10.00,
            totalPower: -841.00,
            mosTemp: 34,
            envTemp: 35,
            cellTemp: "33°c/32°c",
            totalMileage: 15.2,
            voltageDiff: 0.005,
            speed: 0,
            cycles: 1,
            avgVoltage: 4.209,
            maxVoltage: 4.212,
            minVoltage: 4.207,
            batteryCode: "BT2072055010032504140408",
            updateTime: "2025年06月18日11时02分",
            chargeCurrent: 10,
            dischargeStatus: "关",
            balanceStatus: "开",
            batteryStatus: "充电",
            cellVoltages: [
                4.208, 4.209, 4.210, 4.209, 4.207,
                4.209, 4.212, 4.210, 4.209, 4.210,
                4.210, 4.210, 4.209, 4.208, 4.208,
                4.209, 4.210, 4.210, 4.208, 4.207
            ],
            alerts: [
                "定时休眠打开",
                "放电MOS正常闭合",
                "电池充电中标志",
                "充电MOS正常闭合"
            ]
        };

        let currentDischargeCurrent = 60;

        // 初始化页面
        function initPage() {
            updateSOCDisplay();
            updateBatteryInfo();
            generateCellGrid();
            updateCurrentDisplay();
        }

        // 更新SOC显示
        function updateSOCDisplay() {
            const socCircle = document.getElementById('socCircle');
            const socText = document.getElementById('socText');

            const percentage = batteryData.soc * 3.6; // 转换为360度
            socCircle.style.setProperty('--percentage', percentage + 'deg');
            socText.textContent = batteryData.soc + '%';
        }

        // 更新电池信息
        function updateBatteryInfo() {
            document.getElementById('chargeCurrent').textContent = batteryData.chargeCurrent + 'A';
            document.getElementById('dischargeStatus').textContent = batteryData.dischargeStatus;
            document.getElementById('balanceStatus').textContent = batteryData.balanceStatus;
            document.getElementById('batteryStatus').textContent = batteryData.batteryStatus;

            document.getElementById('batteryCode').textContent = '电池编号: ' + batteryData.batteryCode;
            document.getElementById('updateTime').textContent = '更新时间: ' + batteryData.updateTime;

            document.getElementById('totalVoltage').textContent = batteryData.totalVoltage + 'v';
            document.getElementById('totalCurrent').textContent = batteryData.totalCurrent + 'A';
            document.getElementById('totalPower').textContent = batteryData.totalPower + 'w';
            document.getElementById('mosTemp').textContent = batteryData.mosTemp + '°c';
            document.getElementById('envTemp').textContent = batteryData.envTemp + '°c';
            document.getElementById('cellTemp').textContent = batteryData.cellTemp;
            document.getElementById('totalMileage').textContent = batteryData.totalMileage + 'km';
            document.getElementById('voltageDiff').textContent = batteryData.voltageDiff + 'v';

            // 详情页数据
            document.getElementById('detailMileage').textContent = batteryData.totalMileage + 'km';
            document.getElementById('detailSpeed').textContent = batteryData.speed + 'km/h';
            document.getElementById('detailCycles').textContent = batteryData.cycles + '次';
            document.getElementById('detailAvgVoltage').textContent = batteryData.avgVoltage + 'v';
            document.getElementById('detailMaxVoltage').textContent = batteryData.maxVoltage + 'v';
            document.getElementById('detailMinVoltage').textContent = batteryData.minVoltage + 'v';
            document.getElementById('detailMosTemp').textContent = batteryData.mosTemp + '°c';
            document.getElementById('detailEnvTemp').textContent = batteryData.envTemp + '°c';
            document.getElementById('detailCellTemp').textContent = batteryData.cellTemp;
        }

        // 生成电芯网格
        function generateCellGrid() {
            const cellGrid = document.getElementById('cellGrid');
            cellGrid.innerHTML = '';

            batteryData.cellVoltages.forEach((voltage, index) => {
                const cellItem = document.createElement('div');
                cellItem.className = 'cell-item';

                // 判断电压高低
                if (voltage >= batteryData.maxVoltage - 0.001) {
                    cellItem.classList.add('high');
                } else if (voltage <= batteryData.minVoltage + 0.001) {
                    cellItem.classList.add('low');
                }

                cellItem.innerHTML = `
                    <div class="cell-number">${index + 1}</div>
                    <div class="cell-voltage">${voltage}v</div>
                `;

                cellGrid.appendChild(cellItem);
            });
        }

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 移除所有底部按钮的active类
            const footerBtns = document.querySelectorAll('.footer-btn');
            footerBtns.forEach(btn => btn.classList.remove('active'));

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 添加对应标签的active类
            const activeTab = Array.from(tabs).find(tab => tab.textContent.includes(getTabText(tabName)));
            if (activeTab) activeTab.classList.add('active');

            // 添加对应底部按钮的active类
            const activeFooterBtn = Array.from(footerBtns).find(btn => btn.onclick.toString().includes(tabName));
            if (activeFooterBtn) activeFooterBtn.classList.add('active');
        }

        function getTabText(tabName) {
            const tabTexts = {
                'home': '首页',
                'details': '详情',
                'cells': '电芯',
                'control': '控制'
            };
            return tabTexts[tabName] || '';
        }

        // 发送控制命令
        function sendCommand(command) {
            console.log('发送命令:', command);

            // 模拟命令执行
            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = '执行中...';

            setTimeout(() => {
                button.disabled = false;
                button.textContent = originalText;

                // 显示成功消息
                showMessage('命令执行成功: ' + getCommandText(command), 'success');
            }, 1500);
        }

        function getCommandText(command) {
            const commandTexts = {
                'reset': '终端复位',
                'buzzer_on': '开启蜂鸣',
                'buzzer_off': '关闭蜂鸣',
                'allow_discharge': '允许放电',
                'forbid_discharge': '禁止放电',
                'allow_charge': '允许充电',
                'forbid_charge': '禁止充电'
            };
            return commandTexts[command] || command;
        }

        // 设置放电电流
        function setDischargeCurrent(current) {
            currentDischargeCurrent = current;
            updateCurrentDisplay();

            // 更新按钮状态
            const currentBtns = document.querySelectorAll('.current-btn');
            currentBtns.forEach(btn => btn.classList.remove('active'));

            const activeBtn = Array.from(currentBtns).find(btn => btn.textContent === current + 'A');
            if (activeBtn) activeBtn.classList.add('active');

            showMessage(`放电电流已设置为 ${current}A`, 'success');
        }

        // 更新电流显示
        function updateCurrentDisplay() {
            document.getElementById('currentDisplay').textContent = currentDischargeCurrent + 'A';
        }

        // 显示详情
        function showDetails() {
            switchTab('details');
        }

        // 解绑电池
        function unbindBattery() {
            if (confirm('确定要解绑当前电池吗？')) {
                showMessage('电池解绑成功', 'success');
            }
        }

        // 切换电池
        function switchBattery() {
            showMessage('暂无其他电池可切换', 'error');
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.left = '50%';
            messageDiv.style.transform = 'translateX(-50%)';
            messageDiv.style.zIndex = '9999';
            messageDiv.style.maxWidth = '300px';

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                document.body.removeChild(messageDiv);
            }, 3000);
        }

        // 模拟实时数据更新
        function simulateRealTimeData() {
            setInterval(() => {
                // 模拟SOC变化
                if (batteryData.soc < 100) {
                    batteryData.soc += Math.random() * 0.1;
                    if (batteryData.soc > 100) batteryData.soc = 100;
                }

                // 模拟电压微调
                batteryData.totalVoltage += (Math.random() - 0.5) * 0.1;

                // 模拟温度变化
                batteryData.mosTemp += (Math.random() - 0.5) * 0.5;
                batteryData.envTemp += (Math.random() - 0.5) * 0.3;

                // 更新显示
                updateSOCDisplay();
                updateBatteryInfo();
            }, 5000); // 每5秒更新一次
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPage();
            simulateRealTimeData();
        });
    </script>
</body>
</html>
