<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>万洋智行 - 锂电池监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .card h3 {
            color: #1e3c72;
            margin-bottom: 25px;
            font-size: 1.4rem;
            font-weight: 600;
            border-bottom: 3px solid #1e3c72;
            padding-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .card h3::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #1e3c72;
            border-radius: 50%;
            margin-right: 10px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 18px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            border-left: 4px solid #1e3c72;
            transition: all 0.3s ease;
        }

        .metric:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateX(5px);
        }

        .metric-label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .metric-value {
            font-weight: bold;
            color: #1e3c72;
            font-size: 1.2rem;
        }

        .soc-display {
            text-align: center;
            margin-bottom: 25px;
        }

        .soc-circle {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: conic-gradient(from -90deg, #4CAF50 0%, #4CAF50 var(--percentage), #e8f5e9 var(--percentage), #e8f5e9 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
            box-shadow: 0 8px 30px rgba(76, 175, 80, 0.3);
        }

        .soc-circle::before {
            content: '';
            width: 110px;
            height: 110px;
            background: white;
            border-radius: 50%;
            position: absolute;
            box-shadow: inset 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .soc-text {
            font-size: 28px;
            font-weight: bold;
            color: #2e7d32;
            z-index: 1;
        }

        .soc-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .status-good {
            color: #4CAF50;
            font-weight: 600;
        }

        .status-warning {
            color: #FF9800;
            font-weight: 600;
        }

        .status-danger {
            color: #F44336;
            font-weight: 600;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 25px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(30, 60, 114, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(30, 60, 114, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #adb5bd;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .cell-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
            gap: 12px;
            margin-top: 20px;
        }

        .cell-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 12px 8px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .cell-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .cell-item.high {
            border-color: #ff9800;
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            box-shadow: 0 6px 20px rgba(255, 152, 0, 0.3);
        }

        .cell-item.low {
            border-color: #2196f3;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.3);
        }

        .cell-number {
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .cell-voltage {
            font-size: 13px;
            font-weight: bold;
            color: #495057;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 5px solid #dc3545;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        }

        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 5px solid #28a745;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border: none;
            color: white;
            font-size: 28px;
            cursor: pointer;
            box-shadow: 0 8px 30px rgba(30, 60, 114, 0.4);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1) rotate(180deg);
            box-shadow: 0 12px 40px rgba(30, 60, 114, 0.5);
        }

        .alert-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .alert-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-left: 5px solid #ffc107;
            border-radius: 10px;
            margin-bottom: 12px;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
        }

        .alert-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #856404;
        }

        .alert-text {
            color: #856404;
            font-size: 14px;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 20px;
            }
            
            .refresh-btn {
                width: 60px;
                height: 60px;
                font-size: 24px;
                bottom: 20px;
                right: 20px;
            }
        }
<body>
    <div class="container">
        <div class="header">
            <h1>万洋智行</h1>
            <p>锂电池智能监控系统</p>
        </div>

        <div class="dashboard">
            <!-- SOC显示卡片 -->
            <div class="card">
                <h3>电池状态</h3>
                <div class="soc-display">
                    <div class="soc-circle" id="socCircle">
                        <div class="soc-text" id="socText">--</div>
                    </div>
                    <div class="soc-label">电池电量</div>
                </div>
                <div class="metric">
                    <span class="metric-label">充电状态</span>
                    <span class="metric-value" id="chargeStatus">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">放电状态</span>
                    <span class="metric-value" id="dischargeStatus">--</span>
                </div>
            </div>

            <!-- 电压电流卡片 -->
            <div class="card">
                <h3>电压电流</h3>
                <div class="metric">
                    <span class="metric-label">总电压</span>
                    <span class="metric-value" id="totalVoltage">--V</span>
                </div>
                <div class="metric">
                    <span class="metric-label">总电流</span>
                    <span class="metric-value" id="totalCurrent">--A</span>
                </div>
                <div class="metric">
                    <span class="metric-label">总功率</span>
                    <span class="metric-value" id="totalPower">--W</span>
                </div>
                <div class="metric">
                    <span class="metric-label">平均电压</span>
                    <span class="metric-value" id="avgVoltage">--V</span>
                </div>
                <div class="metric">
                    <span class="metric-label">最高电压</span>
                    <span class="metric-value" id="maxVoltage">--V</span>
                </div>
                <div class="metric">
                    <span class="metric-label">最低电压</span>
                    <span class="metric-value" id="minVoltage">--V</span>
                </div>
            </div>

            <!-- 温度信息卡片 -->
            <div class="card">
                <h3>温度监控</h3>
                <div class="metric">
                    <span class="metric-label">BMS温度</span>
                    <span class="metric-value" id="bmsTemp">--°C</span>
                </div>
                <div class="metric">
                    <span class="metric-label">环境温度</span>
                    <span class="metric-value" id="envTemp">--°C</span>
                </div>
                <div class="metric">
                    <span class="metric-label">电芯温度</span>
                    <span class="metric-value" id="cellTemp">--°C</span>
                </div>
            </div>

            <!-- 位置信息卡片 -->
            <div class="card">
                <h3>位置信息</h3>
                <div class="metric">
                    <span class="metric-label">纬度</span>
                    <span class="metric-value" id="latitude">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">经度</span>
                    <span class="metric-value" id="longitude">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">速度</span>
                    <span class="metric-value" id="speed">--km/h</span>
                </div>
                <div class="metric">
                    <span class="metric-label">总里程</span>
                    <span class="metric-value" id="totalMileage">--km</span>
                </div>
                <div class="metric">
                    <span class="metric-label">GSM信号</span>
                    <span class="metric-value" id="gsmSignal">--</span>
                </div>
            </div>

            <!-- 电芯电压卡片 -->
            <div class="card" style="grid-column: 1 / -1;">
                <h3>电芯电压监控</h3>
                <div class="cell-grid" id="cellGrid">
                    <!-- 电芯数据将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 告警信息 -->
        <div class="alert-section" id="alertSection" style="display: none;">
            <h3>告警信息</h3>
            <div id="alertList">
                <!-- 告警项目将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="card">
            <h3>系统控制</h3>
            <div class="controls">
                <button class="btn" onclick="refreshData()">刷新数据</button>
                <button class="btn" onclick="exportData()">导出数据</button>
                <button class="btn" onclick="showHistory()">历史记录</button>
                <button class="btn" onclick="showSettings()">系统设置</button>
            </div>
        </div>
    </div>

    <!-- 刷新按钮 -->
    <button class="refresh-btn" onclick="refreshData()" title="刷新数据">
        🔄
    </button>

    <script>
        let batteryData = {};
        let refreshInterval;

        // 初始化页面
        function initPage() {
            console.log('初始化页面...');
            refreshData();
            startAutoRefresh();
        }

        // 获取电池数据
        async function fetchBatteryData() {
            try {
                const response = await fetch('/api/battery/data');
                if (response.ok) {
                    const data = await response.json();
                    return data;
                } else {
                    throw new Error('获取数据失败');
                }
            } catch (error) {
                console.error('获取电池数据失败:', error);
                showError('获取数据失败: ' + error.message);
                return null;
            }
        }

        // 刷新数据
        async function refreshData() {
            console.log('刷新数据...');

            try {
                const response = await fetch('/api/battery/refresh');
                const result = await response.json();

                if (result.success && result.data) {
                    batteryData = result.data;
                    updateDisplay();
                    showSuccess('数据刷新成功');
                } else {
                    throw new Error(result.message || '刷新失败');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                showError('刷新数据失败: ' + error.message);
            }
        }

        // 更新显示
        function updateDisplay() {
            console.log('更新显示...', batteryData);

            // 更新SOC显示
            updateSOCDisplay();

            // 更新基本信息
            updateElement('chargeStatus', batteryData.charge_status ? '充电中' : '未充电');
            updateElement('dischargeStatus', batteryData.discharge_status ? '放电中' : '未放电');

            // 更新电压电流
            updateElement('totalVoltage', (batteryData.total_voltage || 0).toFixed(2) + 'V');
            updateElement('totalCurrent', (batteryData.total_current || 0).toFixed(2) + 'A');
            updateElement('totalPower', (batteryData.total_power || 0).toFixed(2) + 'W');
            updateElement('avgVoltage', (batteryData.avg_voltage || 0).toFixed(3) + 'V');
            updateElement('maxVoltage', (batteryData.max_voltage || 0).toFixed(3) + 'V');
            updateElement('minVoltage', (batteryData.min_voltage || 0).toFixed(3) + 'V');

            // 更新温度
            updateElement('bmsTemp', (batteryData.bms_temp || 0) + '°C');
            updateElement('envTemp', (batteryData.env_temp || 0) + '°C');
            updateElement('cellTemp', formatCellTemp(batteryData.temp_detail || []));

            // 更新位置信息
            updateElement('latitude', (batteryData.latitude || 0).toFixed(6));
            updateElement('longitude', (batteryData.longitude || 0).toFixed(6));
            updateElement('speed', (batteryData.speed || 0).toFixed(1) + 'km/h');
            updateElement('totalMileage', (batteryData.total_mileage || 0).toFixed(1) + 'km');
            updateElement('gsmSignal', getSignalText(batteryData.gsm_signal || 0));

            // 更新电芯电压
            updateCellGrid();

            // 更新告警信息
            updateAlerts();
        }

        // 更新SOC显示
        function updateSOCDisplay() {
            const soc = batteryData.soc || 0;
            const socCircle = document.getElementById('socCircle');
            const socText = document.getElementById('socText');

            const percentage = soc * 3.6; // 转换为360度
            socCircle.style.setProperty('--percentage', percentage + 'deg');
            socText.textContent = soc + '%';
        }

        // 更新元素内容
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }

        // 格式化电芯温度
        function formatCellTemp(tempDetail) {
            if (!tempDetail || tempDetail.length === 0) {
                return '--°C';
            }

            const temps = tempDetail.map(t => Math.round(t));
            if (temps.length === 1) {
                return temps[0] + '°C';
            } else {
                return Math.min(...temps) + '°C/' + Math.max(...temps) + '°C';
            }
        }

        // 获取信号强度文本
        function getSignalText(signal) {
            if (signal >= 20) return '优 (' + signal + ')';
            if (signal >= 15) return '良 (' + signal + ')';
            if (signal >= 10) return '中 (' + signal + ')';
            if (signal > 0) return '弱 (' + signal + ')';
            return '无信号';
        }

        // 更新电芯网格
        function updateCellGrid() {
            const cellGrid = document.getElementById('cellGrid');
            const cellVoltages = batteryData.cell_voltages || [];

            cellGrid.innerHTML = '';

            cellVoltages.forEach((voltage, index) => {
                const cellItem = document.createElement('div');
                cellItem.className = 'cell-item';

                // 判断电压高低
                const maxVoltage = batteryData.max_voltage || 0;
                const minVoltage = batteryData.min_voltage || 0;

                if (voltage >= maxVoltage - 0.001) {
                    cellItem.classList.add('high');
                } else if (voltage <= minVoltage + 0.001) {
                    cellItem.classList.add('low');
                }

                cellItem.innerHTML = `
                    <div class="cell-number">${index + 1}</div>
                    <div class="cell-voltage">${voltage.toFixed(3)}V</div>
                `;

                cellGrid.appendChild(cellItem);
            });
        }

        // 更新告警信息
        function updateAlerts() {
            const alertSection = document.getElementById('alertSection');
            const alertList = document.getElementById('alertList');
            const alerts = batteryData.alerts || [];
            const protections = batteryData.protections || [];

            const allAlerts = [...alerts, ...protections];

            if (allAlerts.length > 0) {
                alertSection.style.display = 'block';
                alertList.innerHTML = '';

                allAlerts.forEach(alert => {
                    const alertItem = document.createElement('div');
                    alertItem.className = 'alert-item';
                    alertItem.innerHTML = `
                        <div class="alert-icon">⚠️</div>
                        <div class="alert-text">${alert}</div>
                    `;
                    alertList.appendChild(alertItem);
                });
            } else {
                alertSection.style.display = 'none';
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            showMessage(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showMessage(message, 'error');
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.left = '50%';
            messageDiv.style.transform = 'translateX(-50%)';
            messageDiv.style.zIndex = '9999';
            messageDiv.style.maxWidth = '400px';

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                if (document.body.contains(messageDiv)) {
                    document.body.removeChild(messageDiv);
                }
            }, 3000);
        }

        // 开始自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                refreshData();
            }, 30000); // 30秒刷新一次
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // 导出数据
        function exportData() {
            const dataStr = JSON.stringify(batteryData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `battery_data_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            URL.revokeObjectURL(url);
            showSuccess('数据导出成功');
        }

        // 显示历史记录
        function showHistory() {
            showMessage('历史记录功能开发中...', 'success');
        }

        // 显示设置
        function showSettings() {
            showMessage('系统设置功能开发中...', 'success');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initPage();
        });

        // 页面卸载时停止自动刷新
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>
