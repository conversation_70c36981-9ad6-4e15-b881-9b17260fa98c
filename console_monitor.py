#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
万洋锂电池监控系统 - 控制台监控器
Console Monitor for Wanyang Battery Monitoring
"""

import json
import time
import os
import random
from datetime import datetime
from pathlib import Path

class BatteryConsoleMonitor:
    def __init__(self):
        self.battery_data = {
            "soc": 99,
            "total_voltage": 84.10,
            "total_current": -10.00,
            "total_power": -841.00,
            "mos_temp": 34,
            "env_temp": 35,
            "cell_temp_range": "33°C/32°C",
            "total_mileage": 15.2,
            "gsm_signal": "优(23)",
            "battery_code": "BT2072055010032504140408",
            "update_time": "2025年06月18日11时02分",
            "cell_voltages": [
                4.208, 4.209, 4.210, 4.209, 4.207,
                4.209, 4.212, 4.210, 4.209, 4.210,
                4.210, 4.210, 4.209, 4.208, 4.208,
                4.209, 4.210, 4.210, 4.208, 4.207
            ],
            "charge_status": False,
            "discharge_status": True,
            "protection_status": [],
            "alarm_status": []
        }
        
        # 加载真实抓包数据
        self.load_real_data()
    
    def load_real_data(self):
        """加载真实抓包数据"""
        try:
            data_file = Path("data/万洋锂电池抓包数据.json")
            if data_file.exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    real_data = json.load(f)
                    print("✅ 已加载真实抓包数据")
            else:
                print("⚠️ 使用模拟数据")
        except Exception as e:
            print(f"⚠️ 加载数据失败，使用模拟数据: {e}")
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self):
        """打印标题"""
        print("🔋" + "=" * 78 + "🔋")
        print("🔋" + " " * 20 + "万洋锂电池监控系统 - 控制台版本" + " " * 20 + "🔋")
        print("🔋" + " " * 25 + "Wanyang Battery Monitor Console" + " " * 25 + "🔋")
        print("🔋" + "=" * 78 + "🔋")
    
    def print_basic_info(self):
        """打印基本信息"""
        print("\n📊 基本信息")
        print("─" * 80)
        print(f"🔋 电池编号: {self.battery_data['battery_code']}")
        print(f"🕒 更新时间: {self.battery_data['update_time']}")
        print(f"📶 GSM信号: {self.battery_data['gsm_signal']}")
        print(f"🛣️  总里程: {self.battery_data['total_mileage']} km")
    
    def print_soc_display(self):
        """打印SOC显示"""
        soc = self.battery_data['soc']
        print(f"\n⚡ 电池电量 (SOC)")
        print("─" * 80)
        
        # 创建进度条
        bar_length = 50
        filled_length = int(bar_length * soc / 100)
        bar = "█" * filled_length + "░" * (bar_length - filled_length)
        
        # 根据电量显示不同颜色的emoji
        if soc >= 80:
            emoji = "🟢"
        elif soc >= 50:
            emoji = "🟡"
        elif soc >= 20:
            emoji = "🟠"
        else:
            emoji = "🔴"
        
        print(f"{emoji} [{bar}] {soc}%")
        
        # 显示充放电状态
        charge_status = "🔌 充电中" if self.battery_data['charge_status'] else "🔌 未充电"
        discharge_status = "⚡ 放电中" if self.battery_data['discharge_status'] else "⚡ 未放电"
        print(f"   {charge_status}  |  {discharge_status}")
    
    def print_electrical_data(self):
        """打印电气数据"""
        print(f"\n⚡ 电气参数")
        print("─" * 80)
        
        voltage = self.battery_data['total_voltage']
        current = self.battery_data['total_current']
        power = self.battery_data['total_power']
        
        # 格式化显示
        print(f"🔋 总电压: {voltage:>8.2f} V")
        print(f"⚡ 总电流: {current:>8.2f} A")
        print(f"💡 总功率: {power:>8.2f} W")
        
        # 计算电芯统计
        cell_voltages = self.battery_data['cell_voltages']
        avg_voltage = sum(cell_voltages) / len(cell_voltages)
        max_voltage = max(cell_voltages)
        min_voltage = min(cell_voltages)
        voltage_diff = max_voltage - min_voltage
        
        print(f"📊 平均电压: {avg_voltage:>6.3f} V")
        print(f"📈 最高电压: {max_voltage:>6.3f} V")
        print(f"📉 最低电压: {min_voltage:>6.3f} V")
        print(f"📏 压差: {voltage_diff:>10.3f} V")
    
    def print_temperature_data(self):
        """打印温度数据"""
        print(f"\n🌡️  温度监控")
        print("─" * 80)
        
        mos_temp = self.battery_data['mos_temp']
        env_temp = self.battery_data['env_temp']
        cell_temp = self.battery_data['cell_temp_range']
        
        # 温度状态判断
        def get_temp_status(temp):
            if temp < 0:
                return "🟦 过冷"
            elif temp < 10:
                return "🟨 偏冷"
            elif temp < 45:
                return "🟢 正常"
            elif temp < 60:
                return "🟠 偏热"
            else:
                return "🔴 过热"
        
        print(f"🔥 MOS温度: {mos_temp:>6}°C  {get_temp_status(mos_temp)}")
        print(f"🌡️  环境温度: {env_temp:>6}°C  {get_temp_status(env_temp)}")
        print(f"🔋 电芯温度: {cell_temp:>10}")
    
    def print_cell_voltages(self):
        """打印电芯电压"""
        print(f"\n🔋 电芯电压监控 (共{len(self.battery_data['cell_voltages'])}节)")
        print("─" * 80)
        
        cell_voltages = self.battery_data['cell_voltages']
        max_voltage = max(cell_voltages)
        min_voltage = min(cell_voltages)
        
        # 每行显示5个电芯
        for i in range(0, len(cell_voltages), 5):
            row_cells = cell_voltages[i:i+5]
            row_str = ""
            
            for j, voltage in enumerate(row_cells):
                cell_num = i + j + 1
                
                # 标记最高最低电压
                if voltage == max_voltage:
                    status = "🔴"  # 最高
                elif voltage == min_voltage:
                    status = "🔵"  # 最低
                else:
                    status = "⚪"  # 正常
                
                row_str += f"{status} {cell_num:2d}:{voltage:.3f}V  "
            
            print(row_str)
        
        print(f"\n🔴 最高电压  🔵 最低电压  ⚪ 正常电压")
    
    def print_api_data(self):
        """打印API接口数据"""
        print(f"\n📡 API接口数据")
        print("─" * 80)
        
        # 模拟API响应数据
        api_data = {
            "device_id": self.battery_data['battery_code'],
            "timestamp": int(time.time()),
            "data": {
                "soc": self.battery_data['soc'],
                "voltage": self.battery_data['total_voltage'],
                "current": self.battery_data['total_current'],
                "temperature": {
                    "mos": self.battery_data['mos_temp'],
                    "env": self.battery_data['env_temp']
                },
                "cells": self.battery_data['cell_voltages']
            }
        }
        
        # 格式化JSON输出
        json_str = json.dumps(api_data, indent=2, ensure_ascii=False)
        print(json_str)
    
    def print_status_bar(self):
        """打印状态栏"""
        print("\n" + "─" * 80)
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"🕒 当前时间: {current_time}  |  🔄 自动刷新: 5秒  |  ❌ 按Ctrl+C退出")
        print("=" * 80)
    
    def update_data(self):
        """更新数据（模拟数据变化）"""
        # 模拟SOC变化
        self.battery_data['soc'] = min(100, max(0, 
            self.battery_data['soc'] + random.uniform(-0.1, 0.1)))
        
        # 模拟电压电流变化
        self.battery_data['total_voltage'] += random.uniform(-0.05, 0.05)
        self.battery_data['total_current'] += random.uniform(-0.5, 0.5)
        self.battery_data['total_power'] = (
            self.battery_data['total_voltage'] * self.battery_data['total_current'])
        
        # 模拟温度变化
        self.battery_data['mos_temp'] += random.randint(-1, 1)
        self.battery_data['env_temp'] += random.randint(-1, 1)
        
        # 更新时间
        self.battery_data['update_time'] = datetime.now().strftime('%Y年%m月%d日%H时%M分')
        
        # 模拟电芯电压变化
        for i in range(len(self.battery_data['cell_voltages'])):
            self.battery_data['cell_voltages'][i] += random.uniform(-0.002, 0.002)
            self.battery_data['cell_voltages'][i] = round(
                self.battery_data['cell_voltages'][i], 3)
    
    def run(self):
        """运行监控"""
        print("🚀 启动万洋锂电池控制台监控...")
        print("📊 显示实时数据和API接口信息")
        print("🔄 每5秒自动刷新数据")
        print("❌ 按Ctrl+C退出程序")
        print("\n按回车键开始...")
        input()
        
        try:
            while True:
                self.clear_screen()
                self.print_header()
                self.print_basic_info()
                self.print_soc_display()
                self.print_electrical_data()
                self.print_temperature_data()
                self.print_cell_voltages()
                self.print_api_data()
                self.print_status_bar()
                
                # 等待5秒并更新数据
                time.sleep(5)
                self.update_data()
                
        except KeyboardInterrupt:
            print("\n\n👋 监控程序已退出")
            print("感谢使用万洋锂电池监控系统！")

def main():
    """主函数"""
    monitor = BatteryConsoleMonitor()
    monitor.run()

if __name__ == '__main__':
    main()
